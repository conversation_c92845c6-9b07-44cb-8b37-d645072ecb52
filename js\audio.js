// Audio Management System

class AudioManager {
    constructor() {
        this.audioContext = null;
        this.sounds = {};
        this.musicVolume = 0.3;
        this.sfxVolume = 0.5;
        this.currentMusic = null;
        this.isMuted = false;
        
        this.initializeAudio();
    }
    
    initializeAudio() {
        try {
            // Create audio context
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            // Create gain nodes for volume control
            this.musicGain = this.audioContext.createGain();
            this.sfxGain = this.audioContext.createGain();
            
            this.musicGain.connect(this.audioContext.destination);
            this.sfxGain.connect(this.audioContext.destination);
            
            this.musicGain.gain.value = this.musicVolume;
            this.sfxGain.gain.value = this.sfxVolume;
            
            // Generate procedural sounds
            this.generateSounds();
            
        } catch (error) {
            console.warn('Audio not supported:', error);
        }
    }
    
    generateSounds() {
        // Generate engine sound
        this.sounds.engine = this.createEngineSound();
        
        // Generate collision sound
        this.sounds.collision = this.createCollisionSound();
        
        // Generate brake sound
        this.sounds.brake = this.createBrakeSound();
        
        // Generate level complete sound
        this.sounds.levelComplete = this.createLevelCompleteSound();
        
        // Generate game over sound
        this.sounds.gameOver = this.createGameOverSound();
        
        // Generate background music
        this.sounds.backgroundMusic = this.createBackgroundMusic();
    }
    
    createEngineSound() {
        if (!this.audioContext) return null;
        
        const duration = 1;
        const sampleRate = this.audioContext.sampleRate;
        const buffer = this.audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);
        
        for (let i = 0; i < data.length; i++) {
            const t = i / sampleRate;
            // Create engine-like rumble
            const noise = (Math.random() - 0.5) * 0.1;
            const lowFreq = Math.sin(2 * Math.PI * 80 * t) * 0.3;
            const midFreq = Math.sin(2 * Math.PI * 120 * t) * 0.2;
            data[i] = (lowFreq + midFreq + noise) * Math.exp(-t * 2);
        }
        
        return buffer;
    }
    
    createCollisionSound() {
        if (!this.audioContext) return null;
        
        const duration = 0.5;
        const sampleRate = this.audioContext.sampleRate;
        const buffer = this.audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);
        
        for (let i = 0; i < data.length; i++) {
            const t = i / sampleRate;
            // Create crash-like sound
            const noise = (Math.random() - 0.5) * 0.8;
            const impact = Math.sin(2 * Math.PI * 200 * t) * Math.exp(-t * 8);
            data[i] = (noise + impact) * Math.exp(-t * 3);
        }
        
        return buffer;
    }
    
    createBrakeSound() {
        if (!this.audioContext) return null;
        
        const duration = 0.8;
        const sampleRate = this.audioContext.sampleRate;
        const buffer = this.audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);
        
        for (let i = 0; i < data.length; i++) {
            const t = i / sampleRate;
            // Create brake screech sound
            const screech = Math.sin(2 * Math.PI * 800 * t) * 0.4;
            const noise = (Math.random() - 0.5) * 0.2;
            data[i] = (screech + noise) * Math.exp(-t * 2);
        }
        
        return buffer;
    }
    
    createLevelCompleteSound() {
        if (!this.audioContext) return null;
        
        const duration = 2;
        const sampleRate = this.audioContext.sampleRate;
        const buffer = this.audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);
        
        // Create ascending melody
        const notes = [261.63, 329.63, 392.00, 523.25]; // C, E, G, C
        
        for (let i = 0; i < data.length; i++) {
            const t = i / sampleRate;
            const noteIndex = Math.floor(t * 2) % notes.length;
            const freq = notes[noteIndex];
            const envelope = Math.exp(-t * 0.5);
            data[i] = Math.sin(2 * Math.PI * freq * t) * envelope * 0.3;
        }
        
        return buffer;
    }
    
    createGameOverSound() {
        if (!this.audioContext) return null;
        
        const duration = 1.5;
        const sampleRate = this.audioContext.sampleRate;
        const buffer = this.audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);
        
        // Create descending melody
        const notes = [523.25, 392.00, 329.63, 261.63]; // C, G, E, C
        
        for (let i = 0; i < data.length; i++) {
            const t = i / sampleRate;
            const noteIndex = Math.floor(t * 2.67) % notes.length;
            const freq = notes[noteIndex];
            const envelope = Math.exp(-t * 0.8);
            data[i] = Math.sin(2 * Math.PI * freq * t) * envelope * 0.4;
        }
        
        return buffer;
    }
    
    createBackgroundMusic() {
        if (!this.audioContext) return null;
        
        const duration = 30; // 30 second loop
        const sampleRate = this.audioContext.sampleRate;
        const buffer = this.audioContext.createBuffer(2, duration * sampleRate, sampleRate);
        
        // Create ambient background music
        for (let channel = 0; channel < 2; channel++) {
            const data = buffer.getChannelData(channel);
            
            for (let i = 0; i < data.length; i++) {
                const t = i / sampleRate;
                
                // Create layered ambient tones
                const bass = Math.sin(2 * Math.PI * 55 * t) * 0.1;
                const pad1 = Math.sin(2 * Math.PI * 110 * t) * 0.05;
                const pad2 = Math.sin(2 * Math.PI * 165 * t) * 0.03;
                const texture = (Math.random() - 0.5) * 0.01;
                
                // Add some variation
                const modulation = Math.sin(2 * Math.PI * 0.1 * t) * 0.5 + 0.5;
                
                data[i] = (bass + pad1 + pad2 + texture) * modulation;
            }
        }
        
        return buffer;
    }
    
    playSound(soundName, volume = 1, loop = false) {
        if (!this.audioContext || this.isMuted || !this.sounds[soundName]) return null;
        
        try {
            const source = this.audioContext.createBufferSource();
            const gainNode = this.audioContext.createGain();
            
            source.buffer = this.sounds[soundName];
            source.loop = loop;
            
            gainNode.gain.value = volume;
            
            source.connect(gainNode);
            gainNode.connect(this.sfxGain);
            
            source.start();
            
            return source;
        } catch (error) {
            console.warn('Error playing sound:', error);
            return null;
        }
    }
    
    playEngineSound(speed) {
        if (!this.audioContext || this.isMuted) return;
        
        // Modulate engine sound based on speed
        const pitch = 1 + (speed / 120) * 0.5; // Increase pitch with speed
        const volume = Math.min(0.3, 0.1 + (speed / 120) * 0.2);
        
        if (this.currentEngineSound) {
            this.currentEngineSound.stop();
        }
        
        try {
            const source = this.audioContext.createBufferSource();
            const gainNode = this.audioContext.createGain();
            
            source.buffer = this.sounds.engine;
            source.loop = true;
            source.playbackRate.value = pitch;
            
            gainNode.gain.value = volume;
            
            source.connect(gainNode);
            gainNode.connect(this.sfxGain);
            
            source.start();
            this.currentEngineSound = source;
        } catch (error) {
            console.warn('Error playing engine sound:', error);
        }
    }
    
    stopEngineSound() {
        if (this.currentEngineSound) {
            try {
                this.currentEngineSound.stop();
                this.currentEngineSound = null;
            } catch (error) {
                console.warn('Error stopping engine sound:', error);
            }
        }
    }
    
    playCollisionSound() {
        this.playSound('collision', 0.7);
    }
    
    playBrakeSound() {
        this.playSound('brake', 0.4);
    }
    
    playLevelCompleteSound() {
        this.playSound('levelComplete', 0.6);
    }
    
    playGameOverSound() {
        this.playSound('gameOver', 0.8);
    }
    
    playBackgroundMusic() {
        if (this.currentMusic) {
            this.stopBackgroundMusic();
        }
        
        this.currentMusic = this.playSound('backgroundMusic', this.musicVolume, true);
    }
    
    stopBackgroundMusic() {
        if (this.currentMusic) {
            try {
                this.currentMusic.stop();
                this.currentMusic = null;
            } catch (error) {
                console.warn('Error stopping background music:', error);
            }
        }
    }
    
    setMusicVolume(volume) {
        this.musicVolume = Math.max(0, Math.min(1, volume));
        if (this.musicGain) {
            this.musicGain.gain.value = this.musicVolume;
        }
    }
    
    setSFXVolume(volume) {
        this.sfxVolume = Math.max(0, Math.min(1, volume));
        if (this.sfxGain) {
            this.sfxGain.gain.value = this.sfxVolume;
        }
    }
    
    toggleMute() {
        this.isMuted = !this.isMuted;
        
        if (this.isMuted) {
            this.stopBackgroundMusic();
            this.stopEngineSound();
        } else {
            this.playBackgroundMusic();
        }
        
        return this.isMuted;
    }
    
    // Resume audio context (required for some browsers)
    resumeAudioContext() {
        if (this.audioContext && this.audioContext.state === 'suspended') {
            this.audioContext.resume();
        }
    }
    
    // Call this when user interacts with the page
    enableAudio() {
        this.resumeAudioContext();
    }
}
