// Main Game Engine - Village to World Car Game

class Game {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.isRunning = false;
        this.isPaused = false;
        
        // Game state
        this.currentLevel = 1;
        this.score = 0;
        this.safetyScore = 100;
        this.gameSpeed = 60; // FPS
        
        // Initialize game components
        this.vehicle = new Vehicle(this.canvas.width / 2, this.canvas.height - 100);
        this.environment = new Environment();
        this.levels = new LevelManager();
        this.ui = new UIManager();
        this.audio = new AudioManager();
        
        // Game loop
        this.lastTime = 0;
        this.deltaTime = 0;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.resizeCanvas();
        this.showStartScreen();
        
        // Load initial level
        this.levels.loadLevel(this.currentLevel);
        this.environment.setLevel(this.currentLevel);
    }
    
    setupEventListeners() {
        // Start game button
        document.getElementById('startGame').addEventListener('click', () => {
            this.startGame();
        });
        
        // Next level button
        document.getElementById('nextLevel').addEventListener('click', () => {
            this.nextLevel();
        });
        
        // Restart game button
        document.getElementById('restartGame').addEventListener('click', () => {
            this.restartGame();
        });
        
        // Window resize
        window.addEventListener('resize', () => {
            this.resizeCanvas();
        });
        
        // Vehicle controls
        this.setupVehicleControls();
    }
    
    setupVehicleControls() {
        const steeringWheel = document.getElementById('steeringWheel');
        const accelerator = document.getElementById('accelerator');
        const brake = document.getElementById('brake');
        
        // Steering wheel controls
        let isSteeringPressed = false;
        let steeringStartX = 0;
        
        steeringWheel.addEventListener('mousedown', (e) => {
            isSteeringPressed = true;
            steeringStartX = e.clientX;
        });
        
        steeringWheel.addEventListener('touchstart', (e) => {
            isSteeringPressed = true;
            steeringStartX = e.touches[0].clientX;
            e.preventDefault();
        });
        
        document.addEventListener('mousemove', (e) => {
            if (isSteeringPressed) {
                const deltaX = e.clientX - steeringStartX;
                const steeringAngle = Math.max(-45, Math.min(45, deltaX * 0.5));
                this.vehicle.setSteering(steeringAngle);
                steeringWheel.style.transform = `rotate(${steeringAngle}deg)`;
            }
        });
        
        document.addEventListener('touchmove', (e) => {
            if (isSteeringPressed) {
                const deltaX = e.touches[0].clientX - steeringStartX;
                const steeringAngle = Math.max(-45, Math.min(45, deltaX * 0.5));
                this.vehicle.setSteering(steeringAngle);
                steeringWheel.style.transform = `rotate(${steeringAngle}deg)`;
                e.preventDefault();
            }
        });
        
        document.addEventListener('mouseup', () => {
            isSteeringPressed = false;
            this.vehicle.setSteering(0);
            steeringWheel.style.transform = 'rotate(0deg)';
        });
        
        document.addEventListener('touchend', () => {
            isSteeringPressed = false;
            this.vehicle.setSteering(0);
            steeringWheel.style.transform = 'rotate(0deg)';
        });
        
        // Accelerator controls
        accelerator.addEventListener('mousedown', () => {
            this.vehicle.setAccelerating(true);
        });
        
        accelerator.addEventListener('touchstart', (e) => {
            this.vehicle.setAccelerating(true);
            e.preventDefault();
        });
        
        accelerator.addEventListener('mouseup', () => {
            this.vehicle.setAccelerating(false);
        });
        
        accelerator.addEventListener('touchend', () => {
            this.vehicle.setAccelerating(false);
        });
        
        // Brake controls
        brake.addEventListener('mousedown', () => {
            this.vehicle.setBraking(true);
        });
        
        brake.addEventListener('touchstart', (e) => {
            this.vehicle.setBraking(true);
            e.preventDefault();
        });
        
        brake.addEventListener('mouseup', () => {
            this.vehicle.setBraking(false);
        });
        
        brake.addEventListener('touchend', () => {
            this.vehicle.setBraking(false);
        });
        
        // Keyboard controls (backup)
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowLeft':
                    this.vehicle.setSteering(-30);
                    break;
                case 'ArrowRight':
                    this.vehicle.setSteering(30);
                    break;
                case 'ArrowUp':
                    this.vehicle.setAccelerating(true);
                    break;
                case 'ArrowDown':
                    this.vehicle.setBraking(true);
                    break;
            }
        });
        
        document.addEventListener('keyup', (e) => {
            switch(e.key) {
                case 'ArrowLeft':
                case 'ArrowRight':
                    this.vehicle.setSteering(0);
                    break;
                case 'ArrowUp':
                    this.vehicle.setAccelerating(false);
                    break;
                case 'ArrowDown':
                    this.vehicle.setBraking(false);
                    break;
            }
        });
    }
    
    resizeCanvas() {
        const container = document.getElementById('gameContainer');
        const header = document.getElementById('gameHeader');
        const controls = document.getElementById('gameControls');
        
        this.canvas.width = container.clientWidth;
        this.canvas.height = container.clientHeight - header.clientHeight - 140;
    }
    
    showStartScreen() {
        document.getElementById('startScreen').classList.remove('hidden');
        document.getElementById('levelComplete').classList.add('hidden');
        document.getElementById('gameOver').classList.add('hidden');
    }
    
    hideAllScreens() {
        document.getElementById('startScreen').classList.add('hidden');
        document.getElementById('levelComplete').classList.add('hidden');
        document.getElementById('gameOver').classList.add('hidden');
    }
    
    startGame() {
        this.hideAllScreens();
        this.isRunning = true;
        this.gameLoop();
        this.audio.playBackgroundMusic();
    }
    
    nextLevel() {
        this.currentLevel++;
        if (this.currentLevel > 6) {
            this.gameComplete();
            return;
        }
        
        this.hideAllScreens();
        this.levels.loadLevel(this.currentLevel);
        this.environment.setLevel(this.currentLevel);
        this.vehicle.reset(this.canvas.width / 2, this.canvas.height - 100);
        this.isRunning = true;
        this.gameLoop();
    }
    
    restartGame() {
        this.currentLevel = 1;
        this.score = 0;
        this.safetyScore = 100;
        this.hideAllScreens();
        this.levels.loadLevel(this.currentLevel);
        this.environment.setLevel(this.currentLevel);
        this.vehicle.reset(this.canvas.width / 2, this.canvas.height - 100);
        this.isRunning = true;
        this.gameLoop();
    }
    
    gameComplete() {
        this.isRunning = false;
        document.getElementById('levelCompleteText').textContent = 
            'Congratulations! You have successfully traveled from a small village to the most developed countries in the world!';
        document.getElementById('levelComplete').classList.remove('hidden');
        document.getElementById('nextLevel').textContent = 'Play Again';
        document.getElementById('nextLevel').onclick = () => this.restartGame();
    }
    
    gameOver(reason) {
        this.isRunning = false;
        document.getElementById('gameOverText').textContent = reason;
        document.getElementById('gameOver').classList.remove('hidden');
        this.audio.playGameOverSound();
    }
    
    levelComplete() {
        this.isRunning = false;
        const levelNames = ['', 'Village', 'Mandal', 'District', 'Major City', 'State Capital', 'International'];
        document.getElementById('levelCompleteText').textContent = 
            `Great job! You safely completed ${levelNames[this.currentLevel]}. Ready for the next challenge?`;
        document.getElementById('levelComplete').classList.remove('hidden');
        this.audio.playLevelCompleteSound();
    }
    
    update(deltaTime) {
        if (!this.isRunning || this.isPaused) return;
        
        // Update game components
        this.vehicle.update(deltaTime);
        this.environment.update(deltaTime);
        this.levels.update(deltaTime);
        
        // Check collisions and objectives
        this.checkCollisions();
        this.checkObjectives();
        
        // Update UI
        this.updateUI();
    }
    
    checkCollisions() {
        // Check vehicle collisions with environment
        const collisions = this.environment.checkCollisions(this.vehicle);
        
        if (collisions.length > 0) {
            for (let collision of collisions) {
                if (collision.type === 'obstacle') {
                    this.safetyScore -= 10;
                    this.vehicle.handleCollision(collision);
                    this.audio.playCollisionSound();
                } else if (collision.type === 'boundary') {
                    this.safetyScore -= 5;
                    this.vehicle.handleBoundaryCollision();
                }
            }
            
            if (this.safetyScore <= 0) {
                this.gameOver('Your safety score reached zero. Drive more carefully!');
            }
        }
    }
    
    checkObjectives() {
        const objective = this.levels.getCurrentObjective();
        if (objective && this.levels.checkObjectiveComplete(this.vehicle)) {
            this.score += objective.points;
            this.levelComplete();
        }
    }
    
    updateUI() {
        // Update level info
        const levelNames = ['', 'Village', 'Mandal', 'District', 'Major City', 'State Capital', 'International'];
        document.getElementById('currentLevel').textContent = `Level ${this.currentLevel}: ${levelNames[this.currentLevel]}`;
        document.getElementById('score').textContent = `Score: ${this.score}`;
        
        // Update game stats
        document.getElementById('speed').textContent = `Speed: ${Math.round(this.vehicle.speed)} km/h`;
        document.getElementById('safetyScore').textContent = `Safety: ${this.safetyScore}%`;
        
        // Update objective
        const objective = this.levels.getCurrentObjective();
        if (objective) {
            document.getElementById('currentObjective').textContent = objective.description;
            const progress = this.levels.getObjectiveProgress(this.vehicle);
            document.getElementById('progress').style.width = `${progress}%`;
        }
        
        // Update mini map
        this.ui.updateMiniMap(this.vehicle, this.environment);
    }
    
    render() {
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Render game components
        this.environment.render(this.ctx, this.vehicle);
        this.vehicle.render(this.ctx);
        this.levels.render(this.ctx);
        
        // Render UI elements on canvas
        this.ui.render(this.ctx);
    }
    
    gameLoop(currentTime = 0) {
        if (!this.isRunning) return;
        
        this.deltaTime = (currentTime - this.lastTime) / 1000;
        this.lastTime = currentTime;
        
        // Cap delta time to prevent large jumps
        this.deltaTime = Math.min(this.deltaTime, 1/30);
        
        this.update(this.deltaTime);
        this.render();
        
        requestAnimationFrame((time) => this.gameLoop(time));
    }
}

// Initialize game when page loads
window.addEventListener('load', () => {
    const game = new Game();
});
