// Main Game Engine - Village to World Car Game

class Game {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.isRunning = false;
        this.isPaused = false;

        // Game state
        this.currentLevel = 1;
        this.score = 0;
        this.totalDistance = 0; // Total distance traveled in km
        this.gameSpeed = 60; // FPS

        // Dr. Driving style camera system
        this.cameraY = 0;
        this.cameraAngle = 0;
        this.cameraSmoothing = 0.12; // Smoother like Dr. Driving
        this.driverSeatView = true;
        this.dashboardHeight = 80; // Lower dashboard for better road view

        // Dr. Driving style view system
        this.currentView = 'front';
        this.viewSwitchCooldown = 0;
        this.cameraHeight = 25; // Elevated driver position like Dr. Driving

        // Initialize game components
        this.vehicle = new Vehicle(this.canvas.width / 2, this.canvas.height - 100);
        this.environment = new Environment();
        this.levels = new LevelManager();
        this.ui = new UIManager();
        this.audio = new AudioManager();

        // Game loop
        this.lastTime = 0;
        this.deltaTime = 0;

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.resizeCanvas();
        this.showStartScreen();

        // Load initial level
        this.levels.loadLevel(this.currentLevel);
        this.environment.setLevel(this.currentLevel);
    }

    setupEventListeners() {
        // Start game button
        document.getElementById('startGame').addEventListener('click', () => {
            this.startGame();
        });

        // Next level button
        document.getElementById('nextLevel').addEventListener('click', () => {
            this.nextLevel();
        });

        // Restart game button
        document.getElementById('restartGame').addEventListener('click', () => {
            this.restartGame();
        });

        // Window resize
        window.addEventListener('resize', () => {
            this.resizeCanvas();
        });

        // Vehicle controls
        this.setupVehicleControls();
    }

    setupVehicleControls() {
        const steeringWheel = document.getElementById('steeringWheel');
        const accelerator = document.getElementById('accelerator');
        const brake = document.getElementById('brake');

        // Modern steering wheel controls
        let isSteeringPressed = false;
        let steeringStartAngle = 0;
        let currentSteeringAngle = 0;
        let lastSteeringTime = 0;

        const getAngleFromCenter = (e, element) => {
            const rect = element.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;
            const clientX = e.clientX || (e.touches && e.touches[0].clientX);
            const clientY = e.clientY || (e.touches && e.touches[0].clientY);
            return Math.atan2(clientY - centerY, clientX - centerX) * 180 / Math.PI;
        };

        const smoothSteeringReturn = () => {
            if (!isSteeringPressed && Math.abs(currentSteeringAngle) > 0.5) {
                currentSteeringAngle *= 0.92; // Smooth return to center
                const steeringInput = (currentSteeringAngle / 180) * 45;
                this.vehicle.setSteering(steeringInput);
                steeringWheel.style.transform = `rotate(${currentSteeringAngle}deg)`;
                requestAnimationFrame(smoothSteeringReturn);
            } else if (!isSteeringPressed) {
                currentSteeringAngle = 0;
                this.vehicle.setSteering(0);
                steeringWheel.style.transform = 'rotate(0deg)';
            }
        };

        steeringWheel.addEventListener('mousedown', (e) => {
            isSteeringPressed = true;
            steeringStartAngle = getAngleFromCenter(e, steeringWheel);
            e.preventDefault();
        });

        steeringWheel.addEventListener('touchstart', (e) => {
            isSteeringPressed = true;
            steeringStartAngle = getAngleFromCenter(e, steeringWheel);
            e.preventDefault();
        });

        document.addEventListener('mousemove', (e) => {
            if (isSteeringPressed) {
                const currentAngle = getAngleFromCenter(e, steeringWheel);
                let deltaAngle = currentAngle - steeringStartAngle;

                // Handle angle wrapping
                if (deltaAngle > 180) deltaAngle -= 360;
                if (deltaAngle < -180) deltaAngle += 360;

                currentSteeringAngle += deltaAngle * 0.5;
                currentSteeringAngle = Math.max(-180, Math.min(180, currentSteeringAngle));

                const steeringInput = (currentSteeringAngle / 180) * 45;
                this.vehicle.setSteering(steeringInput);
                steeringWheel.style.transform = `rotate(${currentSteeringAngle}deg)`;

                steeringStartAngle = currentAngle;
            }
        });

        document.addEventListener('touchmove', (e) => {
            if (isSteeringPressed) {
                const currentAngle = getAngleFromCenter(e, steeringWheel);
                let deltaAngle = currentAngle - steeringStartAngle;

                // Handle angle wrapping
                if (deltaAngle > 180) deltaAngle -= 360;
                if (deltaAngle < -180) deltaAngle += 360;

                currentSteeringAngle += deltaAngle * 0.5;
                currentSteeringAngle = Math.max(-180, Math.min(180, currentSteeringAngle));

                const steeringInput = (currentSteeringAngle / 180) * 45;
                this.vehicle.setSteering(steeringInput);
                steeringWheel.style.transform = `rotate(${currentSteeringAngle}deg)`;

                steeringStartAngle = currentAngle;
                e.preventDefault();
            }
        });

        document.addEventListener('mouseup', () => {
            if (isSteeringPressed) {
                isSteeringPressed = false;
                smoothSteeringReturn();
            }
        });

        document.addEventListener('touchend', () => {
            if (isSteeringPressed) {
                isSteeringPressed = false;
                smoothSteeringReturn();
            }
        });

        // Enhanced Accelerator controls with visual feedback
        accelerator.addEventListener('mousedown', () => {
            this.vehicle.setAccelerating(true);
            accelerator.classList.add('active');
            this.audio.enableAudio(); // Enable audio on user interaction
        });

        accelerator.addEventListener('touchstart', (e) => {
            this.vehicle.setAccelerating(true);
            accelerator.classList.add('active');
            this.audio.enableAudio(); // Enable audio on user interaction
            e.preventDefault();
        });

        accelerator.addEventListener('mouseup', () => {
            this.vehicle.setAccelerating(false);
            accelerator.classList.remove('active');
        });

        accelerator.addEventListener('touchend', () => {
            this.vehicle.setAccelerating(false);
            accelerator.classList.remove('active');
        });

        // Enhanced Brake controls with visual feedback
        brake.addEventListener('mousedown', () => {
            this.vehicle.setBraking(true);
            brake.classList.add('active');
        });

        brake.addEventListener('touchstart', (e) => {
            this.vehicle.setBraking(true);
            brake.classList.add('active');
            e.preventDefault();
        });

        brake.addEventListener('mouseup', () => {
            this.vehicle.setBraking(false);
            brake.classList.remove('active');
        });

        brake.addEventListener('touchend', () => {
            this.vehicle.setBraking(false);
            brake.classList.remove('active');
        });

        // Gear selector controls
        document.getElementById('gearP').addEventListener('click', () => {
            this.setGear('P');
        });

        document.getElementById('gearR').addEventListener('click', () => {
            this.setGear('R');
        });

        document.getElementById('gearN').addEventListener('click', () => {
            this.setGear('N');
        });

        document.getElementById('gearD').addEventListener('click', () => {
            this.setGear('D');
        });

        // Keyboard controls (backup)
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowLeft':
                    this.vehicle.setSteering(-30);
                    break;
                case 'ArrowRight':
                    this.vehicle.setSteering(30);
                    break;
                case 'ArrowUp':
                    this.vehicle.setAccelerating(true);
                    break;
                case 'ArrowDown':
                    this.vehicle.setBraking(true);
                    break;
                case 'p':
                case 'P':
                    this.setGear('P');
                    break;
                case 'r':
                case 'R':
                    this.setGear('R');
                    break;
                case 'n':
                case 'N':
                    this.setGear('N');
                    break;
                case 'd':
                case 'D':
                    this.setGear('D');
                    break;
                case 'v':
                case 'V':
                    this.switchView();
                    break;
            }
        });

        document.addEventListener('keyup', (e) => {
            switch(e.key) {
                case 'ArrowLeft':
                case 'ArrowRight':
                    this.vehicle.setSteering(0);
                    break;
                case 'ArrowUp':
                    this.vehicle.setAccelerating(false);
                    break;
                case 'ArrowDown':
                    this.vehicle.setBraking(false);
                    break;
            }
        });
    }

    setGear(gear) {
        this.vehicle.setGear(gear);
        this.updateGearDisplay();
    }

    updateGearDisplay() {
        // Update gear display
        const gearText = document.getElementById('gearText');
        const gearLabel = document.getElementById('gearLabel');

        if (gearText && gearLabel) {
            gearText.textContent = this.vehicle.currentGear;

            const gearLabels = {
                'P': 'PARK',
                'R': 'REVERSE',
                'N': 'NEUTRAL',
                'D': 'DRIVE'
            };

            gearLabel.textContent = gearLabels[this.vehicle.currentGear] || 'UNKNOWN';
        }

        // Update gear button states
        document.querySelectorAll('.gear-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        const activeGearBtn = document.getElementById(`gear${this.vehicle.currentGear}`);
        if (activeGearBtn) {
            activeGearBtn.classList.add('active');
        }

        // Show gear lock warning if needed
        if (this.vehicle.gearLocked) {
            this.showGearLockWarning();
        }
    }

    showGearLockWarning() {
        // Flash the gear display to indicate gear is locked
        const gearDisplay = document.getElementById('gearDisplay');
        if (gearDisplay) {
            gearDisplay.style.borderColor = '#FF0000';
            setTimeout(() => {
                gearDisplay.style.borderColor = '#ffd700';
            }, 200);
        }
    }



    updateCamera() {
        // Update view switch cooldown
        if (this.viewSwitchCooldown > 0) {
            this.viewSwitchCooldown -= 1/60; // Decrease by 1/60 second per frame
        }

        if (this.driverSeatView) {
            // Dr. Driving style camera positioning
            if (this.currentView === 'front') {
                // Front view - elevated driver position like Dr. Driving
                const driverOffset = this.cameraHeight; // Elevated position
                const targetCameraY = this.vehicle.y - driverOffset;
                this.cameraY += (targetCameraY - this.cameraY) * this.cameraSmoothing;

                // Smooth camera following with slight steering response
                this.cameraAngle = this.vehicle.angle + (this.vehicle.steeringAngle * 0.05);
            } else if (this.currentView === 'rear') {
                // Rear view - same elevated position
                const driverOffset = this.cameraHeight;
                const targetCameraY = this.vehicle.y - driverOffset;
                this.cameraY += (targetCameraY - this.cameraY) * this.cameraSmoothing;

                // Rear view with smooth rotation
                this.cameraAngle = this.vehicle.angle + 180 + (this.vehicle.steeringAngle * -0.05);
            }
        } else {
            // Third-person view
            const targetCameraY = this.vehicle.y - this.canvas.height * 0.6;
            this.cameraY += (targetCameraY - this.cameraY) * this.cameraSmoothing;
            this.cameraAngle = 0;
        }
    }

    switchView() {
        // Switch between front and rear view
        if (this.viewSwitchCooldown <= 0) {
            this.currentView = this.currentView === 'front' ? 'rear' : 'front';
            this.viewSwitchCooldown = 0.5; // 0.5 second cooldown
        }
    }

    resizeCanvas() {
        const container = document.getElementById('gameContainer');
        const header = document.getElementById('gameHeader');
        const controls = document.getElementById('gameControls');

        this.canvas.width = container.clientWidth;
        this.canvas.height = container.clientHeight - header.clientHeight - 140;
    }

    showStartScreen() {
        document.getElementById('startScreen').classList.remove('hidden');
        document.getElementById('levelComplete').classList.add('hidden');
        document.getElementById('gameOver').classList.add('hidden');
    }

    hideAllScreens() {
        document.getElementById('startScreen').classList.add('hidden');
        document.getElementById('levelComplete').classList.add('hidden');
        document.getElementById('gameOver').classList.add('hidden');
    }

    startGame() {
        this.hideAllScreens();
        this.isRunning = true;
        this.gameLoop();
        this.audio.playBackgroundMusic();
    }

    nextLevel() {
        this.currentLevel++;
        if (this.currentLevel > 6) {
            this.gameComplete();
            return;
        }

        this.hideAllScreens();
        this.levels.loadLevel(this.currentLevel);
        this.environment.setLevel(this.currentLevel);
        this.vehicle.reset(this.canvas.width / 2, this.canvas.height - 100);
        this.isRunning = true;
        this.gameLoop();
    }

    restartGame() {
        this.currentLevel = 1;
        this.score = 0;
        this.totalDistance = 0;
        this.hideAllScreens();
        this.levels.loadLevel(this.currentLevel);
        this.environment.setLevel(this.currentLevel);
        this.vehicle.reset(this.canvas.width / 2, this.canvas.height - 100);
        this.isRunning = true;
        this.gameLoop();
    }

    gameComplete() {
        this.isRunning = false;
        document.getElementById('levelCompleteText').textContent =
            'Congratulations! You have successfully traveled from a small village to the most developed countries in the world!';
        document.getElementById('levelComplete').classList.remove('hidden');
        document.getElementById('nextLevel').textContent = 'Play Again';
        document.getElementById('nextLevel').onclick = () => this.restartGame();
    }

    gameOver(reason) {
        this.isRunning = false;
        document.getElementById('gameOverText').textContent = reason;
        document.getElementById('gameOver').classList.remove('hidden');
        this.audio.playGameOverSound();
    }

    levelComplete() {
        this.isRunning = false;
        const levelNames = ['', 'Village', 'Mandal', 'District', 'Major City', 'State Capital', 'International'];
        document.getElementById('levelCompleteText').textContent =
            `Great job! You safely completed ${levelNames[this.currentLevel]}. Ready for the next challenge?`;
        document.getElementById('levelComplete').classList.remove('hidden');
        this.audio.playLevelCompleteSound();
    }

    update(deltaTime) {
        if (!this.isRunning || this.isPaused) return;

        // Update game components
        this.vehicle.update(deltaTime);
        this.environment.update(deltaTime, this.vehicle);
        this.levels.update(deltaTime);

        // Update camera to follow vehicle smoothly
        this.updateCamera();

        // Update audio based on vehicle state
        if (this.vehicle.isAccelerating || this.vehicle.speed > 5) {
            this.audio.playEngineSound(this.vehicle.speed);
        } else {
            this.audio.stopEngineSound();
        }

        if (this.vehicle.isBraking && this.vehicle.speed > 10) {
            this.audio.playBrakeSound();
        }

        // Update distance traveled
        this.updateDistance(deltaTime);

        // Check objectives
        this.checkObjectives();

        // Update UI
        this.updateUI();
    }

    updateDistance(deltaTime) {
        // Calculate distance traveled based on vehicle speed
        // Convert speed from km/h to km per frame, then accumulate
        if (this.vehicle.speed > 0) {
            const distanceThisFrame = (this.vehicle.speed / 3600) * deltaTime; // km/h to km/s
            this.totalDistance += distanceThisFrame;
        }
    }

    checkObjectives() {
        const objective = this.levels.getCurrentObjective();
        if (objective && this.levels.checkObjectiveComplete(this.vehicle)) {
            this.score += objective.points;
            this.levelComplete();
        }
    }

    updateUI() {
        // Update level info
        const levelNames = ['', 'Village', 'Mandal', 'District', 'Major City', 'State Capital', 'International'];
        document.getElementById('currentLevel').textContent = `Level ${this.currentLevel}: ${levelNames[this.currentLevel]}`;
        document.getElementById('score').textContent = `Score: ${this.score}`;

        // Update game stats
        document.getElementById('speed').textContent = `Speed: ${Math.round(this.vehicle.speed)} km/h`;
        document.getElementById('distance').textContent = `Distance: ${this.totalDistance.toFixed(1)} km`;

        // Update dashboard speedometer
        const speedText = document.getElementById('speedText');
        const speedNeedle = document.getElementById('speedNeedle');
        const gearText = document.getElementById('gearText');
        const gearLabel = document.getElementById('gearLabel');

        if (speedText) {
            speedText.textContent = Math.round(Math.abs(this.vehicle.speed));
        }

        if (speedNeedle) {
            // Rotate needle based on speed (0-120 km/h maps to -90 to 90 degrees)
            const maxSpeed = 120;
            const speedPercent = Math.min(Math.abs(this.vehicle.speed) / maxSpeed, 1);
            const needleAngle = -90 + (speedPercent * 180);
            speedNeedle.style.transform = `translateX(-50%) rotate(${needleAngle}deg)`;
        }

        // Update gear display
        this.updateGearDisplay();

        // Update objective
        const objective = this.levels.getCurrentObjective();
        if (objective) {
            document.getElementById('currentObjective').textContent = objective.description;
            const progress = this.levels.getObjectiveProgress(this.vehicle);
            document.getElementById('progress').style.width = `${progress}%`;
        }

        // Update mini map
        this.ui.updateMiniMap(this.vehicle, this.environment);
    }

    render() {
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // Render game components
        this.environment.render(this.ctx, this.vehicle, this);
        this.vehicle.render(this.ctx, this);
        this.levels.render(this.ctx, this.vehicle);

        // Render UI elements on canvas
        this.ui.render(this.ctx);
    }

    gameLoop(currentTime = 0) {
        if (!this.isRunning) return;

        this.deltaTime = (currentTime - this.lastTime) / 1000;
        this.lastTime = currentTime;

        // Cap delta time to prevent large jumps
        this.deltaTime = Math.min(this.deltaTime, 1/30);

        this.update(this.deltaTime);
        this.render();

        requestAnimationFrame((time) => this.gameLoop(time));
    }
}

// Initialize game when page loads
window.addEventListener('load', () => {
    const game = new Game();
});
