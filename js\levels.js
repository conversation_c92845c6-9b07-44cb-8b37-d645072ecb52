// Level Management and Progression System

class LevelManager {
    constructor() {
        this.currentLevel = 1;
        this.objectives = {};
        this.checkpoints = [];
        this.levelData = this.initializeLevelData();
        this.currentObjective = null;
    }

    initializeLevelData() {
        return {
            1: { // Village
                name: 'Village',
                description: 'Navigate through the peaceful village roads',
                objectives: [
                    {
                        id: 'reach_market',
                        description: 'Drive to the village market (5 km)',
                        type: 'reach_point',
                        target: { x: 400, y: -2000 },
                        radius: 100,
                        points: 100,
                        timeLimit: 300 // seconds
                    }
                ],
                maxSpeed: 60,
                safetyRequirement: 80,
                environment: 'rural'
            },
            2: { // Mandal
                name: 'Mandal',
                description: 'Drive through the developing town',
                objectives: [
                    {
                        id: 'reach_town_center',
                        description: 'Navigate to the town center safely',
                        type: 'reach_point',
                        target: { x: 400, y: -800 },
                        radius: 60,
                        points: 150,
                        timeLimit: 150
                    }
                ],
                maxSpeed: 70,
                safetyRequirement: 75,
                environment: 'town'
            },
            3: { // District
                name: 'District',
                description: 'Navigate through the district headquarters',
                objectives: [
                    {
                        id: 'reach_district_office',
                        description: 'Drive to the district administrative office',
                        type: 'reach_point',
                        target: { x: 400, y: -1200 },
                        radius: 70,
                        points: 200,
                        timeLimit: 180
                    }
                ],
                maxSpeed: 80,
                safetyRequirement: 70,
                environment: 'district'
            },
            4: { // Major City
                name: 'Major City',
                description: 'Navigate through busy city traffic',
                objectives: [
                    {
                        id: 'reach_city_center',
                        description: 'Drive through traffic to reach the city center',
                        type: 'reach_point',
                        target: { x: 400, y: -1600 },
                        radius: 80,
                        points: 300,
                        timeLimit: 240
                    }
                ],
                maxSpeed: 90,
                safetyRequirement: 65,
                environment: 'city'
            },
            5: { // State Capital
                name: 'State Capital',
                description: 'Navigate through the state capital',
                objectives: [
                    {
                        id: 'reach_capitol_building',
                        description: 'Drive to the state capitol building',
                        type: 'reach_point',
                        target: { x: 400, y: -2000 },
                        radius: 90,
                        points: 400,
                        timeLimit: 300
                    }
                ],
                maxSpeed: 100,
                safetyRequirement: 60,
                environment: 'capital'
            },
            6: { // International
                name: 'International',
                description: 'Navigate through the international metropolis',
                objectives: [
                    {
                        id: 'reach_international_airport',
                        description: 'Drive to the international airport',
                        type: 'reach_point',
                        target: { x: 400, y: -2500 },
                        radius: 100,
                        points: 500,
                        timeLimit: 360
                    }
                ],
                maxSpeed: 120,
                safetyRequirement: 55,
                environment: 'international'
            }
        };
    }

    loadLevel(level) {
        this.currentLevel = level;
        const levelInfo = this.levelData[level];

        if (!levelInfo) {
            console.error(`Level ${level} not found`);
            return;
        }

        // Set current objective
        this.currentObjective = levelInfo.objectives[0];

        // Initialize checkpoints
        this.initializeCheckpoints(levelInfo);

        // Update UI
        this.updateLevelUI(levelInfo);

        console.log(`Loaded level ${level}: ${levelInfo.name}`);
    }

    initializeCheckpoints(levelInfo) {
        this.checkpoints = [];

        // Add start checkpoint
        this.checkpoints.push({
            id: 'start',
            x: 400,
            y: 0,
            radius: 30,
            type: 'start',
            passed: true
        });

        // Add intermediate checkpoints based on level
        const checkpointCount = Math.min(3, this.currentLevel);
        const targetY = this.currentObjective.target.y;

        for (let i = 1; i <= checkpointCount; i++) {
            const progress = i / (checkpointCount + 1);
            this.checkpoints.push({
                id: `checkpoint_${i}`,
                x: 400 + (Math.random() - 0.5) * 100,
                y: targetY * progress,
                radius: 40,
                type: 'checkpoint',
                passed: false
            });
        }

        // Add final objective checkpoint
        this.checkpoints.push({
            id: 'objective',
            x: this.currentObjective.target.x,
            y: this.currentObjective.target.y,
            radius: this.currentObjective.radius,
            type: 'objective',
            passed: false
        });
    }

    updateLevelUI(levelInfo) {
        document.getElementById('currentLevel').textContent =
            `Level ${this.currentLevel}: ${levelInfo.name}`;
        document.getElementById('currentObjective').textContent =
            this.currentObjective.description;
    }

    update(deltaTime) {
        // Update any time-based level mechanics
        if (this.currentObjective && this.currentObjective.timeLimit) {
            this.currentObjective.timeRemaining =
                (this.currentObjective.timeRemaining || this.currentObjective.timeLimit) - deltaTime;

            if (this.currentObjective.timeRemaining <= 0) {
                // Time limit exceeded
                return { failed: true, reason: 'Time limit exceeded!' };
            }
        }

        return { failed: false };
    }

    checkObjectiveComplete(vehicle) {
        if (!this.currentObjective) return false;

        const distance = Math.sqrt(
            Math.pow(vehicle.x - this.currentObjective.target.x, 2) +
            Math.pow(vehicle.y - this.currentObjective.target.y, 2)
        );

        return distance <= this.currentObjective.radius;
    }

    getObjectiveProgress(vehicle) {
        if (!this.currentObjective) return 0;

        // Calculate progress based on distance to objective
        const startY = 0;
        const targetY = this.currentObjective.target.y;
        const currentY = vehicle.y;

        const totalDistance = Math.abs(targetY - startY);
        const traveledDistance = Math.abs(currentY - startY);

        return Math.min(100, (traveledDistance / totalDistance) * 100);
    }

    checkCheckpoints(vehicle) {
        let passedNew = false;

        this.checkpoints.forEach(checkpoint => {
            if (!checkpoint.passed) {
                const distance = Math.sqrt(
                    Math.pow(vehicle.x - checkpoint.x, 2) +
                    Math.pow(vehicle.y - checkpoint.y, 2)
                );

                if (distance <= checkpoint.radius) {
                    checkpoint.passed = true;
                    passedNew = true;

                    // Award points for checkpoint
                    if (checkpoint.type === 'checkpoint') {
                        // Award checkpoint bonus
                        console.log(`Checkpoint ${checkpoint.id} passed!`);
                    }
                }
            }
        });

        return passedNew;
    }

    getCurrentObjective() {
        return this.currentObjective;
    }

    getLevelData(level = this.currentLevel) {
        return this.levelData[level];
    }

    getSpeedLimit() {
        const levelInfo = this.levelData[this.currentLevel];
        return levelInfo ? levelInfo.maxSpeed : 60;
    }

    getSafetyRequirement() {
        const levelInfo = this.levelData[this.currentLevel];
        return levelInfo ? levelInfo.safetyRequirement : 80;
    }

    isSpeedViolation(speed) {
        return speed > this.getSpeedLimit();
    }

    calculateLevelScore(completionTime, safetyScore, speedViolations) {
        const basePoints = this.currentObjective ? this.currentObjective.points : 100;

        // Time bonus (faster completion = more points)
        const timeLimit = this.currentObjective ? this.currentObjective.timeLimit : 120;
        const timeBonus = Math.max(0, (timeLimit - completionTime) * 2);

        // Safety bonus
        const safetyBonus = Math.max(0, (safetyScore - this.getSafetyRequirement()) * 5);

        // Speed violation penalty
        const speedPenalty = speedViolations * 10;

        return Math.max(0, basePoints + timeBonus + safetyBonus - speedPenalty);
    }

    render(ctx) {
        // Render checkpoints
        this.renderCheckpoints(ctx);

        // Render objective marker
        this.renderObjectiveMarker(ctx);

        // Render level-specific UI elements
        this.renderLevelUI(ctx);
    }

    renderCheckpoints(ctx) {
        this.checkpoints.forEach(checkpoint => {
            if (checkpoint.type === 'start') return; // Don't render start checkpoint

            const alpha = checkpoint.passed ? 0.3 : 0.7;
            const color = checkpoint.type === 'objective' ? '#FFD700' : '#00FF00';

            // Checkpoint circle
            ctx.save();
            ctx.globalAlpha = alpha;
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            ctx.setLineDash(checkpoint.passed ? [5, 5] : []);

            ctx.beginPath();
            ctx.arc(checkpoint.x, checkpoint.y, checkpoint.radius, 0, Math.PI * 2);
            ctx.stroke();

            if (!checkpoint.passed) {
                ctx.fillStyle = `${color}20`;
                ctx.fill();
            }

            ctx.restore();

            // Checkpoint label
            if (!checkpoint.passed) {
                ctx.fillStyle = color;
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(
                    checkpoint.type === 'objective' ? 'GOAL' : 'CHECKPOINT',
                    checkpoint.x,
                    checkpoint.y - checkpoint.radius - 10
                );
            }
        });
    }

    renderObjectiveMarker(ctx) {
        if (!this.currentObjective) return;

        const target = this.currentObjective.target;

        // Pulsing objective marker
        const pulseScale = 1 + Math.sin(Date.now() * 0.005) * 0.2;

        ctx.save();
        ctx.translate(target.x, target.y);
        ctx.scale(pulseScale, pulseScale);

        // Outer ring
        ctx.strokeStyle = '#FFD700';
        ctx.lineWidth = 4;
        ctx.beginPath();
        ctx.arc(0, 0, this.currentObjective.radius, 0, Math.PI * 2);
        ctx.stroke();

        // Inner fill
        ctx.fillStyle = 'rgba(255, 215, 0, 0.2)';
        ctx.fill();

        // Center marker
        ctx.fillStyle = '#FFD700';
        ctx.beginPath();
        ctx.arc(0, 0, 8, 0, Math.PI * 2);
        ctx.fill();

        ctx.restore();

        // Objective arrow (pointing down to objective)
        const canvas = ctx.canvas;
        const arrowX = canvas.width / 2;
        const arrowY = 50;

        ctx.save();
        ctx.translate(arrowX, arrowY);

        // Arrow background
        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        ctx.fillRect(-30, -15, 60, 30);

        // Arrow border
        ctx.strokeStyle = '#FFD700';
        ctx.lineWidth = 2;
        ctx.strokeRect(-30, -15, 60, 30);

        // Arrow text
        ctx.fillStyle = '#FFD700';
        ctx.font = 'bold 12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('GOAL', 0, 5);

        // Distance text
        const vehicle = arguments[1]; // Vehicle passed from render call
        if (vehicle) {
            const distance = Math.sqrt(
                Math.pow(vehicle.x - target.x, 2) +
                Math.pow(vehicle.y - target.y, 2)
            );

            ctx.font = '10px Arial';
            ctx.fillText(`${Math.round(distance)}m`, 0, -5);
        }

        ctx.restore();
    }

    renderLevelUI(ctx) {
        // Render time remaining if applicable
        if (this.currentObjective && this.currentObjective.timeLimit) {
            const timeRemaining = this.currentObjective.timeRemaining || this.currentObjective.timeLimit;

            ctx.save();
            ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
            ctx.fillRect(10, 10, 150, 40);

            ctx.strokeStyle = '#FFD700';
            ctx.lineWidth = 2;
            ctx.strokeRect(10, 10, 150, 40);

            ctx.fillStyle = timeRemaining < 30 ? '#FF4444' : '#FFD700';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('Time:', 20, 30);
            ctx.fillText(`${Math.ceil(timeRemaining)}s`, 70, 30);

            ctx.restore();
        }

        // Render speed limit
        const speedLimit = this.getSpeedLimit();

        ctx.save();
        ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        ctx.beginPath();
        ctx.arc(ctx.canvas.width - 50, 50, 25, 0, Math.PI * 2);
        ctx.fill();

        ctx.strokeStyle = '#FF0000';
        ctx.lineWidth = 3;
        ctx.stroke();

        ctx.fillStyle = '#000000';
        ctx.font = 'bold 14px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(speedLimit.toString(), ctx.canvas.width - 50, 55);

        ctx.restore();
    }
}
