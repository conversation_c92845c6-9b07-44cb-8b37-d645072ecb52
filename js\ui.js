// User Interface Management

class UIManager {
    constructor() {
        this.miniMapScale = 0.1;
        this.miniMapSize = { width: 200, height: 150 };
        this.notifications = [];
        this.maxNotifications = 5;
    }
    
    updateMiniMap(vehicle, environment) {
        const miniMapCanvas = document.getElementById('miniMap');
        if (!miniMapCanvas) return;
        
        // Create mini map canvas if it doesn't exist
        if (!miniMapCanvas.querySelector('canvas')) {
            const canvas = document.createElement('canvas');
            canvas.width = this.miniMapSize.width;
            canvas.height = this.miniMapSize.height;
            canvas.style.width = '100%';
            canvas.style.height = '100%';
            miniMapCanvas.appendChild(canvas);
        }
        
        const canvas = miniMapCanvas.querySelector('canvas');
        const ctx = canvas.getContext('2d');
        
        // Clear mini map
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Background
        ctx.fillStyle = '#2F4F4F';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        // Calculate mini map bounds
        const centerX = vehicle.x * this.miniMapScale;
        const centerY = vehicle.y * this.miniMapScale;
        const offsetX = canvas.width / 2 - centerX;
        const offsetY = canvas.height / 2 - centerY;
        
        ctx.save();
        ctx.translate(offsetX, offsetY);
        
        // Draw roads on mini map
        ctx.fillStyle = '#696969';
        environment.roads.forEach(road => {
            if (road.type === 'main' || road.type === 'side') {
                ctx.fillRect(
                    road.x * this.miniMapScale,
                    road.y * this.miniMapScale,
                    road.width * this.miniMapScale,
                    road.height * this.miniMapScale
                );
            }
        });
        
        // Draw buildings on mini map
        ctx.fillStyle = '#8B4513';
        environment.buildings.forEach(building => {
            ctx.fillRect(
                building.x * this.miniMapScale,
                building.y * this.miniMapScale,
                building.width * this.miniMapScale,
                building.height * this.miniMapScale
            );
        });
        
        // Draw obstacles on mini map
        ctx.fillStyle = '#FF4444';
        environment.obstacles.forEach(obstacle => {
            ctx.fillRect(
                obstacle.x * this.miniMapScale,
                obstacle.y * this.miniMapScale,
                obstacle.width * this.miniMapScale,
                obstacle.height * this.miniMapScale
            );
        });
        
        // Draw vehicle on mini map
        ctx.fillStyle = '#FFD700';
        ctx.save();
        ctx.translate(centerX, centerY);
        ctx.rotate(vehicle.angle * Math.PI / 180);
        ctx.fillRect(-2, -3, 4, 6);
        ctx.restore();
        
        // Draw vehicle direction indicator
        ctx.strokeStyle = '#FFD700';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(centerX, centerY);
        const dirX = centerX + Math.sin(vehicle.angle * Math.PI / 180) * 15;
        const dirY = centerY - Math.cos(vehicle.angle * Math.PI / 180) * 15;
        ctx.lineTo(dirX, dirY);
        ctx.stroke();
        
        ctx.restore();
        
        // Mini map border
        ctx.strokeStyle = '#FFD700';
        ctx.lineWidth = 2;
        ctx.strokeRect(0, 0, canvas.width, canvas.height);
        
        // Mini map compass
        this.drawCompass(ctx, canvas.width - 25, 25, vehicle.angle);
    }
    
    drawCompass(ctx, x, y, vehicleAngle) {
        const radius = 15;
        
        // Compass background
        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        ctx.beginPath();
        ctx.arc(x, y, radius, 0, Math.PI * 2);
        ctx.fill();
        
        // Compass border
        ctx.strokeStyle = '#FFD700';
        ctx.lineWidth = 1;
        ctx.stroke();
        
        // North indicator
        ctx.save();
        ctx.translate(x, y);
        ctx.rotate(-vehicleAngle * Math.PI / 180);
        
        ctx.fillStyle = '#FF0000';
        ctx.beginPath();
        ctx.moveTo(0, -radius + 3);
        ctx.lineTo(-3, -radius + 8);
        ctx.lineTo(3, -radius + 8);
        ctx.closePath();
        ctx.fill();
        
        // N label
        ctx.fillStyle = '#FFFFFF';
        ctx.font = '8px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('N', 0, -radius + 12);
        
        ctx.restore();
    }
    
    addNotification(message, type = 'info', duration = 3000) {
        const notification = {
            id: Date.now(),
            message: message,
            type: type,
            duration: duration,
            startTime: Date.now(),
            alpha: 1
        };
        
        this.notifications.push(notification);
        
        // Remove old notifications if too many
        if (this.notifications.length > this.maxNotifications) {
            this.notifications.shift();
        }
        
        // Auto-remove notification after duration
        setTimeout(() => {
            this.removeNotification(notification.id);
        }, duration);
    }
    
    removeNotification(id) {
        this.notifications = this.notifications.filter(n => n.id !== id);
    }
    
    updateNotifications() {
        const currentTime = Date.now();
        
        this.notifications.forEach(notification => {
            const elapsed = currentTime - notification.startTime;
            const remaining = notification.duration - elapsed;
            
            // Fade out in last 500ms
            if (remaining < 500) {
                notification.alpha = remaining / 500;
            }
        });
        
        // Remove expired notifications
        this.notifications = this.notifications.filter(n => {
            const elapsed = currentTime - n.startTime;
            return elapsed < n.duration;
        });
    }
    
    render(ctx) {
        this.updateNotifications();
        this.renderNotifications(ctx);
        this.renderHUD(ctx);
    }
    
    renderNotifications(ctx) {
        const startY = 100;
        const spacing = 40;
        
        this.notifications.forEach((notification, index) => {
            const y = startY + index * spacing;
            
            ctx.save();
            ctx.globalAlpha = notification.alpha;
            
            // Notification background
            const bgColor = this.getNotificationColor(notification.type);
            ctx.fillStyle = bgColor;
            ctx.fillRect(10, y, 300, 30);
            
            // Notification border
            ctx.strokeStyle = '#FFD700';
            ctx.lineWidth = 2;
            ctx.strokeRect(10, y, 300, 30);
            
            // Notification text
            ctx.fillStyle = '#FFFFFF';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText(notification.message, 20, y + 20);
            
            // Notification icon
            const icon = this.getNotificationIcon(notification.type);
            ctx.fillStyle = '#FFD700';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'right';
            ctx.fillText(icon, 300, y + 20);
            
            ctx.restore();
        });
    }
    
    getNotificationColor(type) {
        switch(type) {
            case 'success': return 'rgba(0, 128, 0, 0.8)';
            case 'warning': return 'rgba(255, 165, 0, 0.8)';
            case 'error': return 'rgba(255, 0, 0, 0.8)';
            case 'info':
            default: return 'rgba(0, 100, 200, 0.8)';
        }
    }
    
    getNotificationIcon(type) {
        switch(type) {
            case 'success': return '✓';
            case 'warning': return '⚠';
            case 'error': return '✗';
            case 'info':
            default: return 'ℹ';
        }
    }
    
    renderHUD(ctx) {
        // Render additional HUD elements that aren't in HTML
        this.renderSpeedometer(ctx);
        this.renderSafetyIndicator(ctx);
    }
    
    renderSpeedometer(ctx) {
        const canvas = ctx.canvas;
        const centerX = canvas.width - 100;
        const centerY = canvas.height - 100;
        const radius = 40;
        
        // Get vehicle speed (this would need to be passed from game)
        // For now, we'll use a placeholder
        const speed = 0; // This should be passed from the game
        const maxSpeed = 120;
        
        // Speedometer background
        ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
        ctx.fill();
        
        // Speedometer border
        ctx.strokeStyle = '#FFD700';
        ctx.lineWidth = 3;
        ctx.stroke();
        
        // Speed marks
        ctx.strokeStyle = '#FFFFFF';
        ctx.lineWidth = 1;
        
        for (let i = 0; i <= 12; i++) {
            const angle = (i / 12) * Math.PI * 2 - Math.PI / 2;
            const startRadius = radius - 8;
            const endRadius = radius - 3;
            
            ctx.beginPath();
            ctx.moveTo(
                centerX + Math.cos(angle) * startRadius,
                centerY + Math.sin(angle) * startRadius
            );
            ctx.lineTo(
                centerX + Math.cos(angle) * endRadius,
                centerY + Math.sin(angle) * endRadius
            );
            ctx.stroke();
        }
        
        // Speed needle
        const speedAngle = (speed / maxSpeed) * Math.PI * 2 - Math.PI / 2;
        ctx.strokeStyle = '#FF0000';
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.moveTo(centerX, centerY);
        ctx.lineTo(
            centerX + Math.cos(speedAngle) * (radius - 10),
            centerY + Math.sin(speedAngle) * (radius - 10)
        );
        ctx.stroke();
        
        // Center dot
        ctx.fillStyle = '#FFD700';
        ctx.beginPath();
        ctx.arc(centerX, centerY, 3, 0, Math.PI * 2);
        ctx.fill();
        
        // Speed text
        ctx.fillStyle = '#FFFFFF';
        ctx.font = 'bold 12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(`${Math.round(speed)}`, centerX, centerY + 25);
        ctx.font = '8px Arial';
        ctx.fillText('km/h', centerX, centerY + 35);
    }
    
    renderSafetyIndicator(ctx) {
        const canvas = ctx.canvas;
        const x = 20;
        const y = canvas.height - 80;
        const width = 200;
        const height = 20;
        
        // Get safety score (this would need to be passed from game)
        const safetyScore = 100; // This should be passed from the game
        
        // Safety bar background
        ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
        ctx.fillRect(x, y, width, height);
        
        // Safety bar border
        ctx.strokeStyle = '#FFD700';
        ctx.lineWidth = 2;
        ctx.strokeRect(x, y, width, height);
        
        // Safety bar fill
        const fillWidth = (safetyScore / 100) * (width - 4);
        let fillColor;
        
        if (safetyScore > 80) fillColor = '#00FF00';
        else if (safetyScore > 60) fillColor = '#FFFF00';
        else if (safetyScore > 40) fillColor = '#FFA500';
        else fillColor = '#FF0000';
        
        ctx.fillStyle = fillColor;
        ctx.fillRect(x + 2, y + 2, fillWidth, height - 4);
        
        // Safety text
        ctx.fillStyle = '#FFFFFF';
        ctx.font = 'bold 12px Arial';
        ctx.textAlign = 'left';
        ctx.fillText('Safety Score', x, y - 5);
        
        ctx.textAlign = 'center';
        ctx.fillStyle = '#000000';
        ctx.fillText(`${safetyScore}%`, x + width / 2, y + 14);
    }
    
    showLevelTransition(fromLevel, toLevel, callback) {
        // Create transition overlay
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: linear-gradient(135deg, #000000 0%, #1e3a8a 50%, #000000 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            opacity: 0;
            transition: opacity 0.5s ease;
        `;
        
        const title = document.createElement('h2');
        title.textContent = `Level ${toLevel} Unlocked!`;
        title.style.cssText = `
            color: #FFD700;
            font-size: 48px;
            margin-bottom: 20px;
            text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
        `;
        
        const subtitle = document.createElement('p');
        const levelNames = ['', 'Village', 'Mandal', 'District', 'Major City', 'State Capital', 'International'];
        subtitle.textContent = `Welcome to ${levelNames[toLevel]}`;
        subtitle.style.cssText = `
            color: #FFFFFF;
            font-size: 24px;
            margin-bottom: 40px;
        `;
        
        const continueBtn = document.createElement('button');
        continueBtn.textContent = 'Continue Journey';
        continueBtn.style.cssText = `
            padding: 15px 30px;
            font-size: 20px;
            font-weight: bold;
            background: linear-gradient(135deg, #FFD700 0%, #1e3a8a 100%);
            color: #000000;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
        `;
        
        continueBtn.addEventListener('click', () => {
            overlay.style.opacity = '0';
            setTimeout(() => {
                document.body.removeChild(overlay);
                if (callback) callback();
            }, 500);
        });
        
        overlay.appendChild(title);
        overlay.appendChild(subtitle);
        overlay.appendChild(continueBtn);
        document.body.appendChild(overlay);
        
        // Fade in
        setTimeout(() => {
            overlay.style.opacity = '1';
        }, 100);
    }
    
    showGameComplete() {
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: linear-gradient(135deg, #000000 0%, #FFD700 50%, #1e3a8a 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            opacity: 0;
            transition: opacity 0.5s ease;
        `;
        
        const title = document.createElement('h1');
        title.textContent = 'Journey Complete!';
        title.style.cssText = `
            color: #FFD700;
            font-size: 64px;
            margin-bottom: 20px;
            text-shadow: 0 0 30px rgba(255, 215, 0, 0.8);
            animation: pulse 2s infinite;
        `;
        
        const message = document.createElement('p');
        message.textContent = 'Congratulations! You have successfully traveled from a small village to the most developed countries in the world!';
        message.style.cssText = `
            color: #FFFFFF;
            font-size: 24px;
            text-align: center;
            max-width: 800px;
            line-height: 1.6;
            margin-bottom: 40px;
        `;
        
        overlay.appendChild(title);
        overlay.appendChild(message);
        document.body.appendChild(overlay);
        
        // Fade in
        setTimeout(() => {
            overlay.style.opacity = '1';
        }, 100);
    }
}
