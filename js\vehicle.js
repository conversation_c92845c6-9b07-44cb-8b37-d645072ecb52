// Vehicle Physics and Controls

class Vehicle {
    constructor(x, y) {
        // Position car in the center of the road
        const canvas = document.getElementById('gameCanvas');
        const roadWidth = 200;
        const roadX = canvas ? canvas.width / 2 - roadWidth / 2 : 300;

        this.x = roadX + roadWidth / 2; // Center of road
        this.y = y;
        this.width = 40;
        this.height = 80;

        // Physics properties
        this.speed = 0;
        this.maxSpeed = 120; // km/h
        this.acceleration = 50;
        this.deceleration = 80;
        this.brakeForce = 150;
        this.friction = 30;

        // Steering
        this.angle = 0;
        this.steeringAngle = 0;
        this.maxSteeringAngle = 45;
        this.steeringSpeed = 120;

        // Velocity components
        this.velocityX = 0;
        this.velocityY = 0;

        // Control states
        this.isAccelerating = false;
        this.isBraking = false;
        this.currentSteering = 0;

        // Visual properties
        this.color = '#FFD700'; // Gold
        this.damaged = false;
        this.damageLevel = 0;

        // Trail effect
        this.trail = [];
        this.maxTrailLength = 20;

        // Exhaust particles
        this.exhaustParticles = [];
        this.maxExhaustParticles = 15;
    }

    setSteering(angle) {
        this.currentSteering = Math.max(-this.maxSteeringAngle,
                                      Math.min(this.maxSteeringAngle, angle));
    }

    setAccelerating(accelerating) {
        this.isAccelerating = accelerating;
    }

    setBraking(braking) {
        this.isBraking = braking;
    }

    update(deltaTime) {
        // Update steering angle with more realistic response
        const targetSteering = this.currentSteering;
        const steeringDiff = targetSteering - this.steeringAngle;
        this.steeringAngle += steeringDiff * this.steeringSpeed * deltaTime * 0.1;

        // Calculate forces with improved physics
        let forwardForce = 0;

        if (this.isAccelerating) {
            forwardForce = this.acceleration * 10; // Increased acceleration force
        } else if (this.isBraking) {
            forwardForce = -this.brakeForce * 5; // Increased brake force
        }

        // Apply friction when not accelerating
        if (!this.isAccelerating && !this.isBraking) {
            if (this.speed > 0) {
                forwardForce = -this.friction * 2;
            } else if (this.speed < 0) {
                forwardForce = this.friction * 2;
            }
        }

        // Update speed with better physics
        this.speed += forwardForce * deltaTime;
        this.speed = Math.max(-this.maxSpeed * 0.3, Math.min(this.maxSpeed, this.speed));

        // Stop if speed is very low
        if (Math.abs(this.speed) < 0.5) {
            this.speed = 0;
        }

        // Realistic car turning physics
        if (this.speed !== 0 && Math.abs(this.steeringAngle) > 0.1) {
            // More realistic turning based on speed
            const speedFactor = Math.abs(this.speed) / this.maxSpeed;
            const turnRate = (this.steeringAngle / this.maxSteeringAngle) * 90 * speedFactor;
            this.angle += turnRate * deltaTime;
        }

        // Calculate velocity components
        const angleRad = this.angle * Math.PI / 180;
        this.velocityX = Math.sin(angleRad) * this.speed * 2; // Increased movement speed
        this.velocityY = -Math.cos(angleRad) * this.speed * 2; // Increased movement speed

        // Update position
        this.x += this.velocityX * deltaTime;
        this.y += this.velocityY * deltaTime;

        // Update trail
        this.updateTrail();

        // Update exhaust particles
        this.updateExhaustParticles(deltaTime);

        // Add exhaust particles when accelerating
        if (this.isAccelerating && this.speed > 10) {
            this.addExhaustParticle();
        }

        // Keep vehicle on screen (with some tolerance)
        this.constrainToScreen();
    }

    updateTrail() {
        if (this.speed > 5) {
            this.trail.push({
                x: this.x,
                y: this.y,
                alpha: 1.0
            });

            if (this.trail.length > this.maxTrailLength) {
                this.trail.shift();
            }
        }

        // Fade trail
        for (let i = 0; i < this.trail.length; i++) {
            this.trail[i].alpha -= 0.05;
        }

        this.trail = this.trail.filter(point => point.alpha > 0);
    }

    addExhaustParticle() {
        if (this.exhaustParticles.length >= this.maxExhaustParticles) {
            this.exhaustParticles.shift();
        }

        // Calculate exhaust position (rear of car)
        const angleRad = this.angle * Math.PI / 180;
        const exhaustX = this.x - Math.sin(angleRad) * this.height * 0.4;
        const exhaustY = this.y + Math.cos(angleRad) * this.height * 0.4;

        this.exhaustParticles.push({
            x: exhaustX + (Math.random() - 0.5) * 10,
            y: exhaustY + (Math.random() - 0.5) * 5,
            vx: (Math.random() - 0.5) * 20 - this.velocityX * 0.1,
            vy: (Math.random() - 0.5) * 20 - this.velocityY * 0.1,
            size: 2 + Math.random() * 4,
            alpha: 0.8,
            life: 1.0
        });
    }

    updateExhaustParticles(deltaTime) {
        this.exhaustParticles.forEach(particle => {
            particle.x += particle.vx * deltaTime;
            particle.y += particle.vy * deltaTime;
            particle.life -= deltaTime * 2;
            particle.alpha = particle.life * 0.8;
            particle.size *= 0.98;
        });

        this.exhaustParticles = this.exhaustParticles.filter(particle => particle.life > 0);
    }

    constrainToScreen() {
        const canvas = document.getElementById('gameCanvas');
        const margin = 50;

        if (this.x < margin) {
            this.x = margin;
            this.velocityX = Math.max(0, this.velocityX);
        }
        if (this.x > canvas.width - margin) {
            this.x = canvas.width - margin;
            this.velocityX = Math.min(0, this.velocityX);
        }
        if (this.y < margin) {
            this.y = margin;
            this.velocityY = Math.max(0, this.velocityY);
        }
        if (this.y > canvas.height - margin) {
            this.y = canvas.height - margin;
            this.velocityY = Math.min(0, this.velocityY);
        }
    }

    handleCollision(collision) {
        // Reduce speed on collision
        this.speed *= 0.5;

        // Add damage
        this.damageLevel += collision.damage || 1;
        this.damaged = this.damageLevel > 0;

        // Bounce effect
        if (collision.normal) {
            const dot = this.velocityX * collision.normal.x + this.velocityY * collision.normal.y;
            this.velocityX -= 2 * dot * collision.normal.x * 0.5;
            this.velocityY -= 2 * dot * collision.normal.y * 0.5;
        }
    }

    handleBoundaryCollision() {
        this.speed *= 0.8;
    }

    reset(x, y) {
        // Always position car in the center of the road
        const canvas = document.getElementById('gameCanvas');
        const roadWidth = 200;
        const roadX = canvas ? canvas.width / 2 - roadWidth / 2 : 300;

        this.x = roadX + roadWidth / 2; // Center of road
        this.y = y;
        this.speed = 0;
        this.angle = 0;
        this.steeringAngle = 0;
        this.velocityX = 0;
        this.velocityY = 0;
        this.damaged = false;
        this.damageLevel = 0;
        this.trail = [];
        this.isAccelerating = false;
        this.isBraking = false;
        this.currentSteering = 0;
    }

    getBounds() {
        const cos = Math.cos(this.angle * Math.PI / 180);
        const sin = Math.sin(this.angle * Math.PI / 180);

        const corners = [
            { x: -this.width/2, y: -this.height/2 },
            { x: this.width/2, y: -this.height/2 },
            { x: this.width/2, y: this.height/2 },
            { x: -this.width/2, y: this.height/2 }
        ];

        return corners.map(corner => ({
            x: this.x + corner.x * cos - corner.y * sin,
            y: this.y + corner.x * sin + corner.y * cos
        }));
    }

    render(ctx) {
        ctx.save();

        // Render trail
        this.renderTrail(ctx);

        // Render exhaust particles
        this.renderExhaustParticles(ctx);

        // Vehicle shadow (rendered first, behind the car)
        ctx.save();
        ctx.translate(this.x + 3, this.y + 5);
        ctx.rotate(this.angle * Math.PI / 180);
        ctx.fillStyle = 'rgba(0, 0, 0, 0.4)';
        ctx.fillRect(-this.width/2, -this.height/2, this.width, this.height);
        ctx.restore();

        // Move to vehicle position and rotate
        ctx.translate(this.x, this.y);
        ctx.rotate(this.angle * Math.PI / 180);

        // 3D Vehicle body with gradient
        const bodyGradient = ctx.createLinearGradient(-this.width/2, -this.height/2, this.width/2, this.height/2);
        if (this.damaged) {
            bodyGradient.addColorStop(0, '#A0522D');
            bodyGradient.addColorStop(0.5, '#8B4513');
            bodyGradient.addColorStop(1, '#654321');
        } else {
            bodyGradient.addColorStop(0, '#FFE082');
            bodyGradient.addColorStop(0.5, this.color);
            bodyGradient.addColorStop(1, '#B8860B');
        }

        ctx.fillStyle = bodyGradient;
        ctx.fillRect(-this.width/2, -this.height/2, this.width, this.height);

        // Vehicle highlight (3D effect)
        ctx.fillStyle = this.damaged ? '#CD853F' : '#FFECB3';
        ctx.fillRect(-this.width/2, -this.height/2, this.width/3, this.height);

        // Vehicle outline with depth
        ctx.strokeStyle = '#000000';
        ctx.lineWidth = 2;
        ctx.strokeRect(-this.width/2, -this.height/2, this.width, this.height);

        // Vehicle details
        this.renderVehicleDetails(ctx);

        // Damage effects
        if (this.damaged) {
            this.renderDamage(ctx);
        }

        ctx.restore();

        // Render speed indicator
        this.renderSpeedIndicator(ctx);
    }

    renderTrail(ctx) {
        if (this.trail.length < 2) return;

        ctx.strokeStyle = `rgba(255, 215, 0, 0.3)`;
        ctx.lineWidth = 3;
        ctx.lineCap = 'round';

        ctx.beginPath();
        ctx.moveTo(this.trail[0].x, this.trail[0].y);

        for (let i = 1; i < this.trail.length; i++) {
            ctx.globalAlpha = this.trail[i].alpha * 0.5;
            ctx.lineTo(this.trail[i].x, this.trail[i].y);
        }

        ctx.stroke();
        ctx.globalAlpha = 1;
    }

    renderExhaustParticles(ctx) {
        this.exhaustParticles.forEach(particle => {
            ctx.save();
            ctx.globalAlpha = particle.alpha;

            // Create gradient for exhaust particle
            const gradient = ctx.createRadialGradient(
                particle.x, particle.y, 0,
                particle.x, particle.y, particle.size
            );
            gradient.addColorStop(0, 'rgba(100, 100, 100, 0.8)');
            gradient.addColorStop(0.5, 'rgba(80, 80, 80, 0.4)');
            gradient.addColorStop(1, 'rgba(60, 60, 60, 0)');

            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            ctx.fill();

            ctx.restore();
        });
    }

    renderVehicleDetails(ctx) {
        // Front windshield with 3D effect
        const windshieldGradient = ctx.createLinearGradient(0, -this.height/2 + 5, 0, -this.height/2 + 20);
        windshieldGradient.addColorStop(0, '#B3E5FC');
        windshieldGradient.addColorStop(0.5, '#87CEEB');
        windshieldGradient.addColorStop(1, '#4FC3F7');
        ctx.fillStyle = windshieldGradient;
        ctx.fillRect(-this.width/3, -this.height/2 + 5, this.width * 2/3, 15);

        // Windshield frame
        ctx.strokeStyle = '#333333';
        ctx.lineWidth = 1;
        ctx.strokeRect(-this.width/3, -this.height/2 + 5, this.width * 2/3, 15);

        // Rear windshield
        ctx.fillStyle = windshieldGradient;
        ctx.fillRect(-this.width/3, this.height/2 - 20, this.width * 2/3, 15);
        ctx.strokeRect(-this.width/3, this.height/2 - 20, this.width * 2/3, 15);

        // Enhanced headlights with glow
        const headlightGradient = ctx.createRadialGradient(0, 0, 0, 0, 0, 8);
        headlightGradient.addColorStop(0, '#FFFFFF');
        headlightGradient.addColorStop(0.7, '#E3F2FD');
        headlightGradient.addColorStop(1, '#BBDEFB');

        ctx.fillStyle = headlightGradient;
        ctx.beginPath();
        ctx.arc(-this.width/2 + 6, -this.height/2 + 3, 4, 0, Math.PI * 2);
        ctx.fill();
        ctx.beginPath();
        ctx.arc(this.width/2 - 6, -this.height/2 + 3, 4, 0, Math.PI * 2);
        ctx.fill();

        // Headlight glow effect
        ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
        ctx.beginPath();
        ctx.arc(-this.width/2 + 6, -this.height/2 + 3, 8, 0, Math.PI * 2);
        ctx.fill();
        ctx.beginPath();
        ctx.arc(this.width/2 - 6, -this.height/2 + 3, 8, 0, Math.PI * 2);
        ctx.fill();

        // Enhanced taillights
        const taillightGradient = ctx.createRadialGradient(0, 0, 0, 0, 0, 4);
        taillightGradient.addColorStop(0, '#FF5252');
        taillightGradient.addColorStop(0.7, '#FF0000');
        taillightGradient.addColorStop(1, '#C62828');

        ctx.fillStyle = taillightGradient;
        ctx.beginPath();
        ctx.arc(-this.width/2 + 6, this.height/2 - 3, 3, 0, Math.PI * 2);
        ctx.fill();
        ctx.beginPath();
        ctx.arc(this.width/2 - 6, this.height/2 - 3, 3, 0, Math.PI * 2);
        ctx.fill();

        // 3D Wheels with rims
        this.renderWheel(ctx, -this.width/2 - 3, -this.height/3 + 6);
        this.renderWheel(ctx, this.width/2 - 3, -this.height/3 + 6);
        this.renderWheel(ctx, -this.width/2 - 3, this.height/3 - 6);
        this.renderWheel(ctx, this.width/2 - 3, this.height/3 - 6);

        // Car roof
        ctx.fillStyle = this.damaged ? '#8B4513' : '#B8860B';
        ctx.fillRect(-this.width/4, -this.height/4, this.width/2, this.height/2);

        // Door handles
        ctx.fillStyle = '#666666';
        ctx.fillRect(-this.width/2 + 1, -5, 2, 3);
        ctx.fillRect(this.width/2 - 3, -5, 2, 3);
    }

    renderWheel(ctx, x, y) {
        ctx.save();
        ctx.translate(x, y);

        // Wheel shadow
        ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
        ctx.beginPath();
        ctx.arc(1, 1, 7, 0, Math.PI * 2);
        ctx.fill();

        // Tire
        ctx.fillStyle = '#1A1A1A';
        ctx.beginPath();
        ctx.arc(0, 0, 6, 0, Math.PI * 2);
        ctx.fill();

        // Rim
        const rimGradient = ctx.createRadialGradient(0, 0, 0, 0, 0, 4);
        rimGradient.addColorStop(0, '#E0E0E0');
        rimGradient.addColorStop(0.7, '#BDBDBD');
        rimGradient.addColorStop(1, '#757575');

        ctx.fillStyle = rimGradient;
        ctx.beginPath();
        ctx.arc(0, 0, 4, 0, Math.PI * 2);
        ctx.fill();

        // Rim spokes
        ctx.strokeStyle = '#424242';
        ctx.lineWidth = 1;
        for (let i = 0; i < 5; i++) {
            const angle = (i / 5) * Math.PI * 2;
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(Math.cos(angle) * 3, Math.sin(angle) * 3);
            ctx.stroke();
        }

        ctx.restore();
    }

    renderDamage(ctx) {
        // Smoke effect
        ctx.fillStyle = `rgba(100, 100, 100, ${0.3 + Math.random() * 0.3})`;
        for (let i = 0; i < this.damageLevel; i++) {
            const smokeX = (Math.random() - 0.5) * this.width;
            const smokeY = this.height/2 + Math.random() * 10;
            ctx.fillRect(smokeX, smokeY, 3, 3);
        }

        // Damage marks
        ctx.fillStyle = '#654321';
        for (let i = 0; i < this.damageLevel; i++) {
            const markX = (Math.random() - 0.5) * this.width;
            const markY = (Math.random() - 0.5) * this.height;
            ctx.fillRect(markX, markY, 2, 2);
        }
    }

    renderSpeedIndicator(ctx) {
        const canvas = document.getElementById('gameCanvas');
        const x = canvas.width - 150;
        const y = canvas.height - 50;

        // Background
        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        ctx.fillRect(x, y, 120, 30);

        // Border
        ctx.strokeStyle = '#FFD700';
        ctx.lineWidth = 2;
        ctx.strokeRect(x, y, 120, 30);

        // Speed bar
        const speedPercent = Math.abs(this.speed) / this.maxSpeed;
        const barWidth = 100 * speedPercent;

        ctx.fillStyle = this.speed > this.maxSpeed * 0.8 ? '#FF4444' : '#00FF00';
        ctx.fillRect(x + 10, y + 10, barWidth, 10);

        // Speed text
        ctx.fillStyle = '#FFFFFF';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(`${Math.round(Math.abs(this.speed))} km/h`, x + 60, y + 20);
    }
}
