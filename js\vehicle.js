// Vehicle Physics and Controls

class Vehicle {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.width = 40;
        this.height = 80;
        
        // Physics properties
        this.speed = 0;
        this.maxSpeed = 120; // km/h
        this.acceleration = 50;
        this.deceleration = 80;
        this.brakeForce = 150;
        this.friction = 30;
        
        // Steering
        this.angle = 0;
        this.steeringAngle = 0;
        this.maxSteeringAngle = 45;
        this.steeringSpeed = 120;
        
        // Velocity components
        this.velocityX = 0;
        this.velocityY = 0;
        
        // Control states
        this.isAccelerating = false;
        this.isBraking = false;
        this.currentSteering = 0;
        
        // Visual properties
        this.color = '#FFD700'; // Gold
        this.damaged = false;
        this.damageLevel = 0;
        
        // Trail effect
        this.trail = [];
        this.maxTrailLength = 20;
    }
    
    setSteering(angle) {
        this.currentSteering = Math.max(-this.maxSteeringAngle, 
                                      Math.min(this.maxSteeringAngle, angle));
    }
    
    setAccelerating(accelerating) {
        this.isAccelerating = accelerating;
    }
    
    setBraking(braking) {
        this.isBraking = braking;
    }
    
    update(deltaTime) {
        // Update steering angle
        const targetSteering = this.currentSteering;
        const steeringDiff = targetSteering - this.steeringAngle;
        this.steeringAngle += steeringDiff * this.steeringSpeed * deltaTime;
        
        // Calculate forces
        let forwardForce = 0;
        
        if (this.isAccelerating) {
            forwardForce = this.acceleration;
        } else if (this.isBraking) {
            forwardForce = -this.brakeForce;
        }
        
        // Apply friction
        if (!this.isAccelerating && !this.isBraking) {
            if (this.speed > 0) {
                forwardForce = -this.friction;
            } else if (this.speed < 0) {
                forwardForce = this.friction;
            }
        }
        
        // Update speed
        this.speed += forwardForce * deltaTime;
        this.speed = Math.max(-this.maxSpeed * 0.3, Math.min(this.maxSpeed, this.speed));
        
        // Stop if speed is very low
        if (Math.abs(this.speed) < 1) {
            this.speed = 0;
        }
        
        // Calculate turning radius based on speed and steering
        if (this.speed !== 0 && this.steeringAngle !== 0) {
            const turnRadius = this.height / Math.tan(Math.abs(this.steeringAngle) * Math.PI / 180);
            const angularVelocity = this.speed / turnRadius;
            this.angle += angularVelocity * Math.sign(this.steeringAngle) * deltaTime;
        }
        
        // Calculate velocity components
        const angleRad = this.angle * Math.PI / 180;
        this.velocityX = Math.sin(angleRad) * this.speed;
        this.velocityY = -Math.cos(angleRad) * this.speed;
        
        // Update position
        this.x += this.velocityX * deltaTime;
        this.y += this.velocityY * deltaTime;
        
        // Update trail
        this.updateTrail();
        
        // Keep vehicle on screen (with some tolerance)
        this.constrainToScreen();
    }
    
    updateTrail() {
        if (this.speed > 5) {
            this.trail.push({
                x: this.x,
                y: this.y,
                alpha: 1.0
            });
            
            if (this.trail.length > this.maxTrailLength) {
                this.trail.shift();
            }
        }
        
        // Fade trail
        for (let i = 0; i < this.trail.length; i++) {
            this.trail[i].alpha -= 0.05;
        }
        
        this.trail = this.trail.filter(point => point.alpha > 0);
    }
    
    constrainToScreen() {
        const canvas = document.getElementById('gameCanvas');
        const margin = 50;
        
        if (this.x < margin) {
            this.x = margin;
            this.velocityX = Math.max(0, this.velocityX);
        }
        if (this.x > canvas.width - margin) {
            this.x = canvas.width - margin;
            this.velocityX = Math.min(0, this.velocityX);
        }
        if (this.y < margin) {
            this.y = margin;
            this.velocityY = Math.max(0, this.velocityY);
        }
        if (this.y > canvas.height - margin) {
            this.y = canvas.height - margin;
            this.velocityY = Math.min(0, this.velocityY);
        }
    }
    
    handleCollision(collision) {
        // Reduce speed on collision
        this.speed *= 0.5;
        
        // Add damage
        this.damageLevel += collision.damage || 1;
        this.damaged = this.damageLevel > 0;
        
        // Bounce effect
        if (collision.normal) {
            const dot = this.velocityX * collision.normal.x + this.velocityY * collision.normal.y;
            this.velocityX -= 2 * dot * collision.normal.x * 0.5;
            this.velocityY -= 2 * dot * collision.normal.y * 0.5;
        }
    }
    
    handleBoundaryCollision() {
        this.speed *= 0.8;
    }
    
    reset(x, y) {
        this.x = x;
        this.y = y;
        this.speed = 0;
        this.angle = 0;
        this.steeringAngle = 0;
        this.velocityX = 0;
        this.velocityY = 0;
        this.damaged = false;
        this.damageLevel = 0;
        this.trail = [];
    }
    
    getBounds() {
        const cos = Math.cos(this.angle * Math.PI / 180);
        const sin = Math.sin(this.angle * Math.PI / 180);
        
        const corners = [
            { x: -this.width/2, y: -this.height/2 },
            { x: this.width/2, y: -this.height/2 },
            { x: this.width/2, y: this.height/2 },
            { x: -this.width/2, y: this.height/2 }
        ];
        
        return corners.map(corner => ({
            x: this.x + corner.x * cos - corner.y * sin,
            y: this.y + corner.x * sin + corner.y * cos
        }));
    }
    
    render(ctx) {
        ctx.save();
        
        // Render trail
        this.renderTrail(ctx);
        
        // Move to vehicle position and rotate
        ctx.translate(this.x, this.y);
        ctx.rotate(this.angle * Math.PI / 180);
        
        // Vehicle body
        ctx.fillStyle = this.damaged ? '#8B4513' : this.color;
        ctx.fillRect(-this.width/2, -this.height/2, this.width, this.height);
        
        // Vehicle outline
        ctx.strokeStyle = '#000000';
        ctx.lineWidth = 2;
        ctx.strokeRect(-this.width/2, -this.height/2, this.width, this.height);
        
        // Vehicle details
        this.renderVehicleDetails(ctx);
        
        // Damage effects
        if (this.damaged) {
            this.renderDamage(ctx);
        }
        
        ctx.restore();
        
        // Render speed indicator
        this.renderSpeedIndicator(ctx);
    }
    
    renderTrail(ctx) {
        if (this.trail.length < 2) return;
        
        ctx.strokeStyle = `rgba(255, 215, 0, 0.3)`;
        ctx.lineWidth = 3;
        ctx.lineCap = 'round';
        
        ctx.beginPath();
        ctx.moveTo(this.trail[0].x, this.trail[0].y);
        
        for (let i = 1; i < this.trail.length; i++) {
            ctx.globalAlpha = this.trail[i].alpha * 0.5;
            ctx.lineTo(this.trail[i].x, this.trail[i].y);
        }
        
        ctx.stroke();
        ctx.globalAlpha = 1;
    }
    
    renderVehicleDetails(ctx) {
        // Front windshield
        ctx.fillStyle = '#87CEEB';
        ctx.fillRect(-this.width/3, -this.height/2 + 5, this.width * 2/3, 15);
        
        // Rear windshield
        ctx.fillRect(-this.width/3, this.height/2 - 20, this.width * 2/3, 15);
        
        // Headlights
        ctx.fillStyle = '#FFFFFF';
        ctx.fillRect(-this.width/2 + 2, -this.height/2, 8, 5);
        ctx.fillRect(this.width/2 - 10, -this.height/2, 8, 5);
        
        // Taillights
        ctx.fillStyle = '#FF0000';
        ctx.fillRect(-this.width/2 + 2, this.height/2 - 5, 8, 5);
        ctx.fillRect(this.width/2 - 10, this.height/2 - 5, 8, 5);
        
        // Wheels
        ctx.fillStyle = '#333333';
        ctx.fillRect(-this.width/2 - 3, -this.height/3, 6, 12);
        ctx.fillRect(this.width/2 - 3, -this.height/3, 6, 12);
        ctx.fillRect(-this.width/2 - 3, this.height/3 - 12, 6, 12);
        ctx.fillRect(this.width/2 - 3, this.height/3 - 12, 6, 12);
    }
    
    renderDamage(ctx) {
        // Smoke effect
        ctx.fillStyle = `rgba(100, 100, 100, ${0.3 + Math.random() * 0.3})`;
        for (let i = 0; i < this.damageLevel; i++) {
            const smokeX = (Math.random() - 0.5) * this.width;
            const smokeY = this.height/2 + Math.random() * 10;
            ctx.fillRect(smokeX, smokeY, 3, 3);
        }
        
        // Damage marks
        ctx.fillStyle = '#654321';
        for (let i = 0; i < this.damageLevel; i++) {
            const markX = (Math.random() - 0.5) * this.width;
            const markY = (Math.random() - 0.5) * this.height;
            ctx.fillRect(markX, markY, 2, 2);
        }
    }
    
    renderSpeedIndicator(ctx) {
        const canvas = document.getElementById('gameCanvas');
        const x = canvas.width - 150;
        const y = canvas.height - 50;
        
        // Background
        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        ctx.fillRect(x, y, 120, 30);
        
        // Border
        ctx.strokeStyle = '#FFD700';
        ctx.lineWidth = 2;
        ctx.strokeRect(x, y, 120, 30);
        
        // Speed bar
        const speedPercent = Math.abs(this.speed) / this.maxSpeed;
        const barWidth = 100 * speedPercent;
        
        ctx.fillStyle = this.speed > this.maxSpeed * 0.8 ? '#FF4444' : '#00FF00';
        ctx.fillRect(x + 10, y + 10, barWidth, 10);
        
        // Speed text
        ctx.fillStyle = '#FFFFFF';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(`${Math.round(Math.abs(this.speed))} km/h`, x + 60, y + 20);
    }
}
