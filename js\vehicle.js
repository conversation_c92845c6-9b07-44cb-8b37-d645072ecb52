// Vehicle Physics and Controls

class Vehicle {
    constructor(x, y) {
        // Position car in the center of the road
        const canvas = document.getElementById('gameCanvas');
        const roadWidth = 200;
        const roadX = canvas ? canvas.width / 2 - roadWidth / 2 : 300;

        this.x = roadX + roadWidth / 2; // Center of road
        this.y = y;
        this.width = 45;
        this.height = 90; // BMW proportions - longer and wider

        // Physics properties
        this.speed = 0;
        this.maxSpeed = 150; // km/h - increased max speed
        this.acceleration = 80; // increased acceleration
        this.deceleration = 100;
        this.brakeForce = 200; // increased brake force
        this.friction = 25; // reduced friction for better movement

        // Steering
        this.angle = 0;
        this.steeringAngle = 0;
        this.maxSteeringAngle = 45;
        this.steeringSpeed = 120;

        // Velocity components
        this.velocityX = 0;
        this.velocityY = 0;

        // Control states
        this.isAccelerating = false;
        this.isBraking = false;
        this.currentSteering = 0;

        // Visual properties - BMW colors
        this.color = '#2C3E50'; // BMW Dark Blue/Gray
        this.bmwBlue = '#0066CC'; // BMW Blue
        this.bmwSilver = '#C0C0C0'; // BMW Silver
        this.damaged = false;
        this.damageLevel = 0;

        // Trail effect
        this.trail = [];
        this.maxTrailLength = 20;

        // Exhaust particles
        this.exhaustParticles = [];
        this.maxExhaustParticles = 15;

        // Road boundary warnings
        this.offRoadWarning = false;
        this.nearLeftBoundary = false;
        this.nearRightBoundary = false;

        // Gear system
        this.currentGear = 'P'; // P, R, N, D
        this.gearLocked = false;
    }

    setSteering(angle) {
        this.currentSteering = Math.max(-this.maxSteeringAngle,
                                      Math.min(this.maxSteeringAngle, angle));
    }

    setAccelerating(accelerating) {
        this.isAccelerating = accelerating;
    }

    setBraking(braking) {
        this.isBraking = braking;
    }

    setGear(gear) {
        // Can only change gear when stopped or very slow
        if (Math.abs(this.speed) < 2) {
            this.currentGear = gear;
            this.gearLocked = false;
        } else {
            this.gearLocked = true;
        }
    }

    canAccelerate() {
        return this.currentGear === 'D' || this.currentGear === 'R';
    }

    canMove() {
        return this.currentGear !== 'P';
    }

    update(deltaTime) {
        // Update steering angle with more realistic response
        const targetSteering = this.currentSteering;
        const steeringDiff = targetSteering - this.steeringAngle;
        this.steeringAngle += steeringDiff * this.steeringSpeed * deltaTime * 0.1;

        // Calculate forces with improved physics
        let forwardForce = 0;

        // Check gear restrictions
        if (this.currentGear === 'P') {
            // Park: No movement allowed, apply parking brake
            this.speed = 0;
            forwardForce = 0;
        } else if (this.currentGear === 'N') {
            // Neutral: No acceleration, only coasting and braking
            if (this.isBraking && this.speed > 0) {
                forwardForce = -this.brakeForce * 5;
            } else if (this.isBraking && this.speed < 0) {
                forwardForce = this.brakeForce * 5;
            }
        } else if (this.currentGear === 'D') {
            // Drive: Forward movement
            if (this.isAccelerating) {
                forwardForce = this.acceleration * 10;
            } else if (this.isBraking && this.speed > 0) {
                forwardForce = -this.brakeForce * 5;
            }
        } else if (this.currentGear === 'R') {
            // Reverse: Backward movement
            if (this.isAccelerating) {
                forwardForce = -this.acceleration * 5; // Slower reverse
            } else if (this.isBraking && this.speed < 0) {
                forwardForce = this.brakeForce * 5; // Brake in reverse
            }
        }

        // Apply friction when not accelerating
        if (!this.isAccelerating && !this.isBraking) {
            if (this.speed > 0) {
                forwardForce = -this.friction * 2;
            }
        }

        // Update speed with better physics
        this.speed += forwardForce * deltaTime;

        // Apply speed limits based on gear
        if (this.currentGear === 'P') {
            this.speed = 0;
        } else if (this.currentGear === 'R') {
            // Reverse gear: limit reverse speed
            this.speed = Math.max(-this.maxSpeed * 0.3, Math.min(0, this.speed));
        } else {
            // Forward gears: limit forward speed
            this.speed = Math.max(0, Math.min(this.maxSpeed, this.speed));
        }

        // Stop if speed is very low
        if (Math.abs(this.speed) < 0.5) {
            this.speed = 0;
        }

        // Realistic car turning physics
        if (this.speed !== 0 && Math.abs(this.steeringAngle) > 0.1) {
            // More realistic turning based on speed
            const speedFactor = Math.abs(this.speed) / this.maxSpeed;
            const turnRate = (this.steeringAngle / this.maxSteeringAngle) * 90 * speedFactor;
            this.angle += turnRate * deltaTime;
        }

        // Calculate velocity components
        const angleRad = this.angle * Math.PI / 180;
        this.velocityX = Math.sin(angleRad) * this.speed * 3; // Further increased movement speed
        this.velocityY = -Math.cos(angleRad) * this.speed * 3; // Further increased movement speed

        // Update position
        this.x += this.velocityX * deltaTime;
        this.y += this.velocityY * deltaTime;

        // Update trail
        this.updateTrail();

        // Update exhaust particles
        this.updateExhaustParticles(deltaTime);

        // Add exhaust particles when accelerating
        if (this.isAccelerating && this.speed > 10) {
            this.addExhaustParticle();
        }

        // Keep vehicle on screen (with some tolerance)
        this.constrainToScreen();
    }

    updateTrail() {
        if (this.speed > 5) {
            this.trail.push({
                x: this.x,
                y: this.y,
                alpha: 1.0
            });

            if (this.trail.length > this.maxTrailLength) {
                this.trail.shift();
            }
        }

        // Fade trail
        for (let i = 0; i < this.trail.length; i++) {
            this.trail[i].alpha -= 0.05;
        }

        this.trail = this.trail.filter(point => point.alpha > 0);
    }

    addExhaustParticle() {
        if (this.exhaustParticles.length >= this.maxExhaustParticles) {
            this.exhaustParticles.shift();
        }

        // Calculate exhaust position (rear of car)
        const angleRad = this.angle * Math.PI / 180;
        const exhaustX = this.x - Math.sin(angleRad) * this.height * 0.4;
        const exhaustY = this.y + Math.cos(angleRad) * this.height * 0.4;

        this.exhaustParticles.push({
            x: exhaustX + (Math.random() - 0.5) * 10,
            y: exhaustY + (Math.random() - 0.5) * 5,
            vx: (Math.random() - 0.5) * 20 - this.velocityX * 0.1,
            vy: (Math.random() - 0.5) * 20 - this.velocityY * 0.1,
            size: 2 + Math.random() * 4,
            alpha: 0.8,
            life: 1.0
        });
    }

    updateExhaustParticles(deltaTime) {
        this.exhaustParticles.forEach(particle => {
            particle.x += particle.vx * deltaTime;
            particle.y += particle.vy * deltaTime;
            particle.life -= deltaTime * 2;
            particle.alpha = particle.life * 0.8;
            particle.size *= 0.98;
        });

        this.exhaustParticles = this.exhaustParticles.filter(particle => particle.life > 0);
    }

    constrainToScreen() {
        const canvas = document.getElementById('gameCanvas');

        // Road constraints - keep car on road only
        const roadWidth = 200;
        const roadX = canvas.width / 2 - roadWidth / 2;
        const roadLeftBoundary = roadX + 20; // Left edge of road + margin
        const roadRightBoundary = roadX + roadWidth - 20; // Right edge of road - margin

        // Check if car is near road boundaries for warning
        const warningZone = 30;
        this.nearLeftBoundary = this.x < roadLeftBoundary + warningZone;
        this.nearRightBoundary = this.x > roadRightBoundary - warningZone;

        // Constrain horizontally to road boundaries
        if (this.x < roadLeftBoundary) {
            this.x = roadLeftBoundary;
            this.velocityX = Math.max(0, this.velocityX);
            this.offRoadWarning = true;
        } else if (this.x > roadRightBoundary) {
            this.x = roadRightBoundary;
            this.velocityX = Math.min(0, this.velocityX);
            this.offRoadWarning = true;
        } else {
            this.offRoadWarning = false;
        }

        // Allow vertical movement along the road (no Y constraints for road travel)
        // Only prevent going too far off screen vertically
        const verticalMargin = 100;
        if (this.y < -canvas.height * 10) { // Allow going very far up (for long distances)
            this.y = -canvas.height * 10;
        }
        if (this.y > canvas.height + verticalMargin) {
            this.y = canvas.height + verticalMargin;
        }
    }

    handleCollision(collision) {
        // Reduce speed on collision
        this.speed *= 0.5;

        // Add damage
        this.damageLevel += collision.damage || 1;
        this.damaged = this.damageLevel > 0;

        // Bounce effect
        if (collision.normal) {
            const dot = this.velocityX * collision.normal.x + this.velocityY * collision.normal.y;
            this.velocityX -= 2 * dot * collision.normal.x * 0.5;
            this.velocityY -= 2 * dot * collision.normal.y * 0.5;
        }
    }

    handleBoundaryCollision() {
        this.speed *= 0.8;
    }

    reset(x, y) {
        // Always position car in the center of the road
        const canvas = document.getElementById('gameCanvas');
        const roadWidth = 200;
        const roadX = canvas ? canvas.width / 2 - roadWidth / 2 : 300;

        this.x = roadX + roadWidth / 2; // Center of road
        this.y = y;
        this.speed = 0;
        this.angle = 0;
        this.steeringAngle = 0;
        this.velocityX = 0;
        this.velocityY = 0;
        this.damaged = false;
        this.damageLevel = 0;
        this.trail = [];
        this.isAccelerating = false;
        this.isBraking = false;
        this.currentSteering = 0;
    }

    getBounds() {
        const cos = Math.cos(this.angle * Math.PI / 180);
        const sin = Math.sin(this.angle * Math.PI / 180);

        const corners = [
            { x: -this.width/2, y: -this.height/2 },
            { x: this.width/2, y: -this.height/2 },
            { x: this.width/2, y: this.height/2 },
            { x: -this.width/2, y: this.height/2 }
        ];

        return corners.map(corner => ({
            x: this.x + corner.x * cos - corner.y * sin,
            y: this.y + corner.x * sin + corner.y * cos
        }));
    }

    render(ctx) {
        ctx.save();

        // Render trail
        this.renderTrail(ctx);

        // Render exhaust particles
        this.renderExhaustParticles(ctx);

        // Move to vehicle position and rotate
        ctx.translate(this.x, this.y);
        ctx.rotate(this.angle * Math.PI / 180);

        // Render 3D car
        this.render3DCar(ctx);

        ctx.restore();

        // Render speed indicator
        this.renderSpeedIndicator(ctx);

        // Render road boundary warnings
        this.renderRoadWarnings(ctx);
    }

    render3DCar(ctx) {
        // 3D Car rendering with multiple layers for depth

        // 1. Car shadow (ground level)
        ctx.save();
        ctx.translate(4, 6);
        ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
        ctx.fillRect(-this.width/2, -this.height/2, this.width, this.height);
        ctx.restore();

        // 2. Wheels (bottom layer)
        this.render3DWheels(ctx);

        // 3. Car undercarriage
        ctx.fillStyle = '#2C2C2C';
        ctx.fillRect(-this.width/2 + 2, -this.height/2 + 2, this.width - 4, this.height - 4);

        // 4. Main car body (3D effect)
        this.render3DBody(ctx);

        // 5. Car roof and cabin
        this.render3DRoof(ctx);

        // 6. Windows and glass
        this.render3DWindows(ctx);

        // 7. Headlights and taillights
        this.render3DLights(ctx);

        // 8. Car details and trim
        this.render3DDetails(ctx);

        // 9. Damage effects
        if (this.damaged) {
            this.renderDamage(ctx);
        }
    }

    render3DBody(ctx) {
        // BMW body with authentic styling
        const bodyGradient = ctx.createLinearGradient(-this.width/2, -this.height/2, this.width/2, 0);
        if (this.damaged) {
            bodyGradient.addColorStop(0, '#A0522D');
            bodyGradient.addColorStop(0.3, '#8B4513');
            bodyGradient.addColorStop(0.7, '#654321');
            bodyGradient.addColorStop(1, '#4A4A4A');
        } else {
            // BMW Metallic Paint
            bodyGradient.addColorStop(0, '#4A90E2');
            bodyGradient.addColorStop(0.2, this.color);
            bodyGradient.addColorStop(0.6, '#1A252F');
            bodyGradient.addColorStop(1, '#0F1419');
        }

        ctx.fillStyle = bodyGradient;
        ctx.fillRect(-this.width/2, -this.height/2, this.width, this.height);

        // BMW characteristic side highlight
        const highlightGradient = ctx.createLinearGradient(-this.width/2, 0, -this.width/4, 0);
        highlightGradient.addColorStop(0, this.damaged ? '#CD853F' : '#E8F4FD');
        highlightGradient.addColorStop(1, 'rgba(232, 244, 253, 0)');

        ctx.fillStyle = highlightGradient;
        ctx.fillRect(-this.width/2, -this.height/2, this.width/3, this.height);

        // BMW body lines and character lines
        ctx.strokeStyle = '#1A1A1A';
        ctx.lineWidth = 2;
        ctx.strokeRect(-this.width/2, -this.height/2, this.width, this.height);

        // BMW characteristic shoulder line
        ctx.strokeStyle = '#4A90E2';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(-this.width/2 + 5, -this.height/4);
        ctx.lineTo(this.width/2 - 5, -this.height/4);
        ctx.stroke();

        // Side panels with BMW styling
        ctx.strokeStyle = '#666666';
        ctx.lineWidth = 1;
        ctx.strokeRect(-this.width/2 + 3, -this.height/2 + 8, this.width - 6, this.height - 16);

        // BMW door separation lines
        ctx.strokeStyle = '#2C3E50';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(-this.width/2 + 8, -this.height/2 + 10);
        ctx.lineTo(-this.width/2 + 8, this.height/2 - 10);
        ctx.moveTo(this.width/2 - 8, -this.height/2 + 10);
        ctx.lineTo(this.width/2 - 8, this.height/2 - 10);
        ctx.stroke();
    }

    render3DRoof(ctx) {
        // BMW roof with sleek design
        const roofWidth = this.width * 0.75;
        const roofHeight = this.height * 0.55;

        // BMW roof gradient
        const roofGradient = ctx.createLinearGradient(0, -roofHeight/2, 0, roofHeight/2);
        if (this.damaged) {
            roofGradient.addColorStop(0, '#8B4513');
            roofGradient.addColorStop(0.5, '#654321');
            roofGradient.addColorStop(1, '#4A4A4A');
        } else {
            roofGradient.addColorStop(0, '#3A5998');
            roofGradient.addColorStop(0.3, this.color);
            roofGradient.addColorStop(0.7, '#1A252F');
            roofGradient.addColorStop(1, '#0F1419');
        }

        ctx.fillStyle = roofGradient;
        ctx.fillRect(-roofWidth/2, -roofHeight/2, roofWidth, roofHeight);

        // BMW roof highlight
        ctx.fillStyle = this.damaged ? '#A0522D' : '#E8F4FD';
        ctx.fillRect(-roofWidth/2, -roofHeight/2, roofWidth/4, roofHeight);

        // Roof outline
        ctx.strokeStyle = '#1A1A1A';
        ctx.lineWidth = 1.5;
        ctx.strokeRect(-roofWidth/2, -roofHeight/2, roofWidth, roofHeight);

        // BMW sunroof (optional)
        if (!this.damaged) {
            ctx.fillStyle = '#2C3E50';
            ctx.fillRect(-roofWidth/3, -roofHeight/3, roofWidth * 2/3, roofHeight * 2/3);
            ctx.strokeStyle = '#4A90E2';
            ctx.lineWidth = 1;
            ctx.strokeRect(-roofWidth/3, -roofHeight/3, roofWidth * 2/3, roofHeight * 2/3);
        }
    }

    render3DWindows(ctx) {
        // Front windshield with 3D effect
        const windshieldGradient = ctx.createLinearGradient(0, -this.height/2 + 8, 0, -this.height/2 + 25);
        windshieldGradient.addColorStop(0, '#E3F2FD');
        windshieldGradient.addColorStop(0.3, '#BBDEFB');
        windshieldGradient.addColorStop(0.7, '#90CAF9');
        windshieldGradient.addColorStop(1, '#64B5F6');

        ctx.fillStyle = windshieldGradient;
        ctx.fillRect(-this.width/3, -this.height/2 + 8, this.width * 2/3, 17);

        // Windshield reflection
        ctx.fillStyle = 'rgba(255, 255, 255, 0.4)';
        ctx.fillRect(-this.width/3, -this.height/2 + 8, this.width/4, 17);

        // Windshield frame
        ctx.strokeStyle = '#2C2C2C';
        ctx.lineWidth = 2;
        ctx.strokeRect(-this.width/3, -this.height/2 + 8, this.width * 2/3, 17);

        // Rear windshield
        ctx.fillStyle = windshieldGradient;
        ctx.fillRect(-this.width/3, this.height/2 - 25, this.width * 2/3, 17);

        // Rear windshield reflection
        ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
        ctx.fillRect(-this.width/3, this.height/2 - 25, this.width/4, 17);

        // Rear windshield frame
        ctx.strokeStyle = '#2C2C2C';
        ctx.lineWidth = 2;
        ctx.strokeRect(-this.width/3, this.height/2 - 25, this.width * 2/3, 17);

        // Side windows
        ctx.fillStyle = windshieldGradient;
        ctx.fillRect(-this.width/2 + 2, -this.height/4, 8, this.height/2);
        ctx.fillRect(this.width/2 - 10, -this.height/4, 8, this.height/2);

        // Side window frames
        ctx.strokeStyle = '#2C2C2C';
        ctx.lineWidth = 1;
        ctx.strokeRect(-this.width/2 + 2, -this.height/4, 8, this.height/2);
        ctx.strokeRect(this.width/2 - 10, -this.height/4, 8, this.height/2);
    }

    render3DLights(ctx) {
        // BMW Angel Eyes Headlights
        const headlightGradient = ctx.createRadialGradient(0, 0, 0, 0, 0, 7);
        headlightGradient.addColorStop(0, '#FFFFFF');
        headlightGradient.addColorStop(0.4, '#F0F8FF');
        headlightGradient.addColorStop(0.8, '#E6E6FA');
        headlightGradient.addColorStop(1, '#D3D3D3');

        // Left BMW headlight
        ctx.save();
        ctx.translate(-this.width/2 + 10, -this.height/2 + 6);

        // Headlight housing
        ctx.fillStyle = '#2C2C2C';
        ctx.fillRect(-8, -4, 16, 8);

        // Main headlight
        ctx.fillStyle = headlightGradient;
        ctx.beginPath();
        ctx.arc(0, 0, 6, 0, Math.PI * 2);
        ctx.fill();

        // BMW Angel Eye ring
        ctx.strokeStyle = '#4A90E2';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.arc(0, 0, 5, 0, Math.PI * 2);
        ctx.stroke();

        // Inner light
        ctx.fillStyle = '#FFFFFF';
        ctx.beginPath();
        ctx.arc(0, 0, 3, 0, Math.PI * 2);
        ctx.fill();

        // Headlight reflection
        ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        ctx.beginPath();
        ctx.arc(-2, -2, 2, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();

        // Right BMW headlight
        ctx.save();
        ctx.translate(this.width/2 - 10, -this.height/2 + 6);

        // Headlight housing
        ctx.fillStyle = '#2C2C2C';
        ctx.fillRect(-8, -4, 16, 8);

        // Main headlight
        ctx.fillStyle = headlightGradient;
        ctx.beginPath();
        ctx.arc(0, 0, 6, 0, Math.PI * 2);
        ctx.fill();

        // BMW Angel Eye ring
        ctx.strokeStyle = '#4A90E2';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.arc(0, 0, 5, 0, Math.PI * 2);
        ctx.stroke();

        // Inner light
        ctx.fillStyle = '#FFFFFF';
        ctx.beginPath();
        ctx.arc(0, 0, 3, 0, Math.PI * 2);
        ctx.fill();

        // Headlight reflection
        ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        ctx.beginPath();
        ctx.arc(-2, -2, 2, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();

        // Enhanced 3D taillights
        const taillightGradient = ctx.createRadialGradient(0, 0, 0, 0, 0, 4);
        taillightGradient.addColorStop(0, '#FF6B6B');
        taillightGradient.addColorStop(0.6, '#FF5252');
        taillightGradient.addColorStop(1, '#D32F2F');

        // Left taillight
        ctx.save();
        ctx.translate(-this.width/2 + 8, this.height/2 - 4);
        ctx.fillStyle = taillightGradient;
        ctx.beginPath();
        ctx.arc(0, 0, 3, 0, Math.PI * 2);
        ctx.fill();

        ctx.strokeStyle = '#8D1E1E';
        ctx.lineWidth = 1;
        ctx.stroke();
        ctx.restore();

        // Right taillight
        ctx.save();
        ctx.translate(this.width/2 - 8, this.height/2 - 4);
        ctx.fillStyle = taillightGradient;
        ctx.beginPath();
        ctx.arc(0, 0, 3, 0, Math.PI * 2);
        ctx.fill();

        ctx.strokeStyle = '#8D1E1E';
        ctx.lineWidth = 1;
        ctx.stroke();
        ctx.restore();

        // Headlight glow effect when moving
        if (this.speed > 5) {
            ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
            ctx.beginPath();
            ctx.arc(-this.width/2 + 8, -this.height/2 + 4, 12, 0, Math.PI * 2);
            ctx.fill();
            ctx.beginPath();
            ctx.arc(this.width/2 - 8, -this.height/2 + 4, 12, 0, Math.PI * 2);
            ctx.fill();
        }
    }

    render3DWheels(ctx) {
        // Enhanced 3D wheels with detailed rims
        this.render3DWheel(ctx, -this.width/2 - 4, -this.height/3 + 8);
        this.render3DWheel(ctx, this.width/2 + 4, -this.height/3 + 8);
        this.render3DWheel(ctx, -this.width/2 - 4, this.height/3 - 8);
        this.render3DWheel(ctx, this.width/2 + 4, this.height/3 - 8);
    }

    render3DWheel(ctx, x, y) {
        ctx.save();
        ctx.translate(x, y);

        // Wheel shadow
        ctx.fillStyle = 'rgba(0, 0, 0, 0.4)';
        ctx.beginPath();
        ctx.arc(2, 2, 10, 0, Math.PI * 2);
        ctx.fill();

        // BMW Performance tire (outer)
        const tireGradient = ctx.createRadialGradient(0, 0, 0, 0, 0, 9);
        tireGradient.addColorStop(0, '#2C2C2C');
        tireGradient.addColorStop(0.6, '#1A1A1A');
        tireGradient.addColorStop(0.9, '#0D0D0D');
        tireGradient.addColorStop(1, '#000000');

        ctx.fillStyle = tireGradient;
        ctx.beginPath();
        ctx.arc(0, 0, 9, 0, Math.PI * 2);
        ctx.fill();

        // BMW tire tread pattern
        ctx.strokeStyle = '#0D0D0D';
        ctx.lineWidth = 1;
        for (let i = 0; i < 12; i++) {
            const angle = (i / 12) * Math.PI * 2;
            ctx.beginPath();
            ctx.moveTo(Math.cos(angle) * 7, Math.sin(angle) * 7);
            ctx.lineTo(Math.cos(angle) * 9, Math.sin(angle) * 9);
            ctx.stroke();
        }

        // BMW alloy rim
        const rimGradient = ctx.createRadialGradient(-1, -1, 0, 0, 0, 6);
        rimGradient.addColorStop(0, '#F8F8F8');
        rimGradient.addColorStop(0.2, '#E8E8E8');
        rimGradient.addColorStop(0.6, '#D0D0D0');
        rimGradient.addColorStop(0.9, '#B8B8B8');
        rimGradient.addColorStop(1, '#A0A0A0');

        ctx.fillStyle = rimGradient;
        ctx.beginPath();
        ctx.arc(0, 0, 6, 0, Math.PI * 2);
        ctx.fill();

        // BMW M-style spokes (double spoke design)
        ctx.strokeStyle = '#888888';
        ctx.lineWidth = 2;
        for (let i = 0; i < 5; i++) {
            const angle = (i / 5) * Math.PI * 2;
            const angle2 = angle + 0.2;

            // First spoke
            ctx.beginPath();
            ctx.moveTo(Math.cos(angle) * 1, Math.sin(angle) * 1);
            ctx.lineTo(Math.cos(angle) * 5, Math.sin(angle) * 5);
            ctx.stroke();

            // Second spoke (slightly offset)
            ctx.beginPath();
            ctx.moveTo(Math.cos(angle2) * 1, Math.sin(angle2) * 1);
            ctx.lineTo(Math.cos(angle2) * 5, Math.sin(angle2) * 5);
            ctx.stroke();
        }

        // BMW center cap with logo
        ctx.fillStyle = '#FFFFFF';
        ctx.beginPath();
        ctx.arc(0, 0, 2.5, 0, Math.PI * 2);
        ctx.fill();

        // BMW logo on center cap
        ctx.strokeStyle = '#0066CC';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.arc(0, 0, 2, 0, Math.PI * 2);
        ctx.stroke();

        // BMW logo quarters
        ctx.fillStyle = '#0066CC';
        ctx.beginPath();
        ctx.arc(0, 0, 1.5, 0, Math.PI, false);
        ctx.fill();

        // Rim highlight
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        ctx.beginPath();
        ctx.arc(-2, -2, 2, 0, Math.PI * 2);
        ctx.fill();

        // Rim edge highlight
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.4)';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.arc(0, 0, 6, 0, Math.PI * 2);
        ctx.stroke();

        ctx.restore();
    }

    render3DDetails(ctx) {
        // BMW Kidney Grille (iconic)
        ctx.save();
        ctx.translate(0, -this.height/2 + 8);

        // Left kidney
        ctx.fillStyle = '#1A1A1A';
        ctx.beginPath();
        ctx.ellipse(-6, 0, 5, 8, 0, 0, Math.PI * 2);
        ctx.fill();
        ctx.strokeStyle = '#C0C0C0';
        ctx.lineWidth = 2;
        ctx.stroke();

        // Right kidney
        ctx.fillStyle = '#1A1A1A';
        ctx.beginPath();
        ctx.ellipse(6, 0, 5, 8, 0, 0, Math.PI * 2);
        ctx.fill();
        ctx.strokeStyle = '#C0C0C0';
        ctx.lineWidth = 2;
        ctx.stroke();

        // Grille slats
        ctx.strokeStyle = '#666666';
        ctx.lineWidth = 1;
        for (let i = -3; i <= 3; i++) {
            ctx.beginPath();
            ctx.moveTo(-10, i * 2);
            ctx.lineTo(-2, i * 2);
            ctx.moveTo(2, i * 2);
            ctx.lineTo(10, i * 2);
            ctx.stroke();
        }
        ctx.restore();

        // BMW Logo area
        ctx.fillStyle = '#FFFFFF';
        ctx.beginPath();
        ctx.arc(0, -this.height/2 + 8, 4, 0, Math.PI * 2);
        ctx.fill();
        ctx.strokeStyle = '#0066CC';
        ctx.lineWidth = 2;
        ctx.stroke();

        // BMW logo quarters
        ctx.fillStyle = '#0066CC';
        ctx.beginPath();
        ctx.arc(0, -this.height/2 + 8, 3, 0, Math.PI, false);
        ctx.fill();

        // Chrome door handles
        ctx.fillStyle = '#E8E8E8';
        ctx.fillRect(-this.width/2 + 2, -3, 4, 2);
        ctx.fillRect(this.width/2 - 6, -3, 4, 2);

        // Door handle highlights
        ctx.fillStyle = '#FFFFFF';
        ctx.fillRect(-this.width/2 + 2, -3, 1, 2);
        ctx.fillRect(this.width/2 - 6, -3, 1, 2);

        // BMW side mirrors (larger, more detailed)
        ctx.fillStyle = this.color;
        ctx.fillRect(-this.width/2 - 3, -this.height/4, 6, 4);
        ctx.fillRect(this.width/2 - 3, -this.height/4, 6, 4);

        // Mirror glass with BMW blue tint
        ctx.fillStyle = '#4A90E2';
        ctx.fillRect(-this.width/2 - 2, -this.height/4 + 0.5, 4, 3);
        ctx.fillRect(this.width/2 - 2, -this.height/4 + 0.5, 4, 3);

        // Mirror highlights
        ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
        ctx.fillRect(-this.width/2 - 2, -this.height/4 + 0.5, 1, 3);
        ctx.fillRect(this.width/2 - 2, -this.height/4 + 0.5, 1, 3);

        // BMW license plate
        ctx.fillStyle = '#FFFFFF';
        ctx.fillRect(-10, this.height/2 - 10, 20, 8);
        ctx.strokeStyle = '#2C2C2C';
        ctx.lineWidth = 1;
        ctx.strokeRect(-10, this.height/2 - 10, 20, 8);

        // License plate text
        ctx.fillStyle = '#000000';
        ctx.font = '6px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('BMW', 0, this.height/2 - 5);

        // BMW antenna
        ctx.strokeStyle = '#C0C0C0';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(this.width/3, -this.height/2 + 10);
        ctx.lineTo(this.width/3, -this.height/2 - 8);
        ctx.stroke();

        // Side indicator lights
        ctx.fillStyle = '#FFA500';
        ctx.beginPath();
        ctx.arc(-this.width/2 + 2, -this.height/4, 2, 0, Math.PI * 2);
        ctx.fill();
        ctx.beginPath();
        ctx.arc(this.width/2 - 2, -this.height/4, 2, 0, Math.PI * 2);
        ctx.fill();
    }

    renderTrail(ctx) {
        if (this.trail.length < 2) return;

        ctx.strokeStyle = `rgba(255, 215, 0, 0.3)`;
        ctx.lineWidth = 3;
        ctx.lineCap = 'round';

        ctx.beginPath();
        ctx.moveTo(this.trail[0].x, this.trail[0].y);

        for (let i = 1; i < this.trail.length; i++) {
            ctx.globalAlpha = this.trail[i].alpha * 0.5;
            ctx.lineTo(this.trail[i].x, this.trail[i].y);
        }

        ctx.stroke();
        ctx.globalAlpha = 1;
    }



    renderExhaustParticles(ctx) {
        this.exhaustParticles.forEach(particle => {
            ctx.save();
            ctx.globalAlpha = particle.alpha;

            // Create gradient for exhaust particle
            const gradient = ctx.createRadialGradient(
                particle.x, particle.y, 0,
                particle.x, particle.y, particle.size
            );
            gradient.addColorStop(0, 'rgba(100, 100, 100, 0.8)');
            gradient.addColorStop(0.5, 'rgba(80, 80, 80, 0.4)');
            gradient.addColorStop(1, 'rgba(60, 60, 60, 0)');

            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            ctx.fill();

            ctx.restore();
        });
    }





    renderDamage(ctx) {
        // Smoke effect
        ctx.fillStyle = `rgba(100, 100, 100, ${0.3 + Math.random() * 0.3})`;
        for (let i = 0; i < this.damageLevel; i++) {
            const smokeX = (Math.random() - 0.5) * this.width;
            const smokeY = this.height/2 + Math.random() * 10;
            ctx.fillRect(smokeX, smokeY, 3, 3);
        }

        // Damage marks
        ctx.fillStyle = '#654321';
        for (let i = 0; i < this.damageLevel; i++) {
            const markX = (Math.random() - 0.5) * this.width;
            const markY = (Math.random() - 0.5) * this.height;
            ctx.fillRect(markX, markY, 2, 2);
        }
    }

    renderSpeedIndicator(ctx) {
        const canvas = document.getElementById('gameCanvas');
        const x = canvas.width - 150;
        const y = canvas.height - 50;

        // Background
        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        ctx.fillRect(x, y, 120, 30);

        // Border
        ctx.strokeStyle = '#FFD700';
        ctx.lineWidth = 2;
        ctx.strokeRect(x, y, 120, 30);

        // Speed bar
        const speedPercent = Math.abs(this.speed) / this.maxSpeed;
        const barWidth = 100 * speedPercent;

        ctx.fillStyle = this.speed > this.maxSpeed * 0.8 ? '#FF4444' : '#00FF00';
        ctx.fillRect(x + 10, y + 10, barWidth, 10);

        // Speed text
        ctx.fillStyle = '#FFFFFF';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(`${Math.round(Math.abs(this.speed))} km/h`, x + 60, y + 20);
    }

    renderRoadWarnings(ctx) {
        const canvas = document.getElementById('gameCanvas');

        // Show warning when car is at road boundaries
        if (this.offRoadWarning) {
            // Flash red warning
            ctx.save();
            ctx.fillStyle = `rgba(255, 0, 0, ${0.3 + Math.sin(Date.now() * 0.01) * 0.2})`;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Warning text
            ctx.fillStyle = '#FF0000';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('STAY ON ROAD!', canvas.width / 2, 100);
            ctx.restore();
        }

        // Show boundary warnings when near edges
        if (this.nearLeftBoundary || this.nearRightBoundary) {
            ctx.save();
            const alpha = 0.1 + Math.sin(Date.now() * 0.005) * 0.1;

            if (this.nearLeftBoundary) {
                ctx.fillStyle = `rgba(255, 165, 0, ${alpha})`;
                ctx.fillRect(0, 0, 50, canvas.height);
            }

            if (this.nearRightBoundary) {
                ctx.fillStyle = `rgba(255, 165, 0, ${alpha})`;
                ctx.fillRect(canvas.width - 50, 0, 50, canvas.height);
            }

            ctx.restore();
        }
    }
}
