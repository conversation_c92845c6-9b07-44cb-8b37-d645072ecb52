// Vehicle Physics and Controls

class Vehicle {
    constructor(x, y) {
        // Position car in the center of the road
        const canvas = document.getElementById('gameCanvas');
        const roadWidth = 200;
        const roadX = canvas ? canvas.width / 2 - roadWidth / 2 : 300;

        this.x = roadX + roadWidth / 2; // Center of road
        this.y = y;
        this.width = 45;
        this.height = 90; // BMW proportions - longer and wider

        // Dr. Driving style physics
        this.speed = 0;
        this.maxSpeed = 80; // km/h - Dr. Driving style speed
        this.acceleration = 35; // Smooth progressive acceleration
        this.deceleration = 60;
        this.brakeForce = 100; // Responsive but smooth braking
        this.friction = 18; // Realistic friction for smooth control

        // Dr. Driving style steering
        this.angle = 0;
        this.steeringAngle = 0;
        this.maxSteeringAngle = 35; // More realistic steering limit
        this.steeringSpeed = 150; // Responsive steering like Dr. Driving
        this.steeringReturn = 0.85; // Auto-return to center

        // Velocity components
        this.velocityX = 0;
        this.velocityY = 0;

        // Control states
        this.isAccelerating = false;
        this.isBraking = false;
        this.currentSteering = 0;

        // Visual properties - BMW colors
        this.color = '#2C3E50'; // BMW Dark Blue/Gray
        this.bmwBlue = '#0066CC'; // BMW Blue
        this.bmwSilver = '#C0C0C0'; // BMW Silver
        this.damaged = false;
        this.damageLevel = 0;

        // Trail effect
        this.trail = [];
        this.maxTrailLength = 20;

        // Exhaust particles
        this.exhaustParticles = [];
        this.maxExhaustParticles = 15;

        // Road boundary warnings
        this.offRoadWarning = false;
        this.nearLeftBoundary = false;
        this.nearRightBoundary = false;

        // Gear system
        this.currentGear = 'P'; // P, R, N, D
        this.gearLocked = false;
    }

    setSteering(angle) {
        this.currentSteering = Math.max(-this.maxSteeringAngle,
                                      Math.min(this.maxSteeringAngle, angle));
    }

    setAccelerating(accelerating) {
        this.isAccelerating = accelerating;
    }

    setBraking(braking) {
        this.isBraking = braking;
    }

    setGear(gear) {
        // Can only change gear when stopped or very slow
        if (Math.abs(this.speed) < 2) {
            this.currentGear = gear;
            this.gearLocked = false;
        } else {
            this.gearLocked = true;
        }
    }

    canAccelerate() {
        return this.currentGear === 'D' || this.currentGear === 'R';
    }

    canMove() {
        return this.currentGear !== 'P';
    }

    update(deltaTime) {
        // Dr. Driving style steering with auto-return
        const targetSteering = this.currentSteering;
        const steeringDiff = targetSteering - this.steeringAngle;

        if (Math.abs(targetSteering) < 0.1) {
            // Auto-return to center when not steering (like Dr. Driving)
            this.steeringAngle *= this.steeringReturn;
        } else {
            // Responsive steering input
            this.steeringAngle += steeringDiff * this.steeringSpeed * deltaTime * 0.08;
        }

        // Calculate forces with improved physics
        let forwardForce = 0;

        // Check gear restrictions
        if (this.currentGear === 'P') {
            // Park: No movement allowed, apply parking brake
            this.speed = 0;
            forwardForce = 0;
        } else if (this.currentGear === 'N') {
            // Neutral: No acceleration, only coasting and braking
            if (this.isBraking && this.speed > 0) {
                forwardForce = -this.brakeForce * 2;
            } else if (this.isBraking && this.speed < 0) {
                forwardForce = this.brakeForce * 2;
            }
        } else if (this.currentGear === 'D') {
            // Drive: Forward movement
            if (this.isAccelerating) {
                forwardForce = this.acceleration * 3; // reduced from 10
            } else if (this.isBraking && this.speed > 0) {
                forwardForce = -this.brakeForce * 2; // reduced from 5
            }
        } else if (this.currentGear === 'R') {
            // Reverse: Backward movement
            if (this.isAccelerating) {
                forwardForce = -this.acceleration * 1.5; // reduced from 5
            } else if (this.isBraking && this.speed < 0) {
                forwardForce = this.brakeForce * 2; // reduced from 5
            }
        }

        // Apply friction when not accelerating
        if (!this.isAccelerating && !this.isBraking) {
            if (this.speed > 0) {
                forwardForce = -this.friction * 2;
            }
        }

        // Update speed with better physics
        this.speed += forwardForce * deltaTime;

        // Apply speed limits based on gear
        if (this.currentGear === 'P') {
            this.speed = 0;
        } else if (this.currentGear === 'R') {
            // Reverse gear: limit reverse speed
            this.speed = Math.max(-this.maxSpeed * 0.3, Math.min(0, this.speed));
        } else {
            // Forward gears: limit forward speed
            this.speed = Math.max(0, Math.min(this.maxSpeed, this.speed));
        }

        // Stop if speed is very low
        if (Math.abs(this.speed) < 0.5) {
            this.speed = 0;
        }

        // Realistic car turning physics
        if (this.speed !== 0 && Math.abs(this.steeringAngle) > 0.1) {
            // More realistic turning based on speed
            const speedFactor = Math.abs(this.speed) / this.maxSpeed;
            const turnRate = (this.steeringAngle / this.maxSteeringAngle) * 90 * speedFactor;
            this.angle += turnRate * deltaTime;
        }

        // Calculate velocity components
        const angleRad = this.angle * Math.PI / 180;
        this.velocityX = Math.sin(angleRad) * this.speed * 1.5; // reduced from 3 for slower movement
        this.velocityY = -Math.cos(angleRad) * this.speed * 1.5; // reduced from 3 for slower movement

        // Update position
        this.x += this.velocityX * deltaTime;
        this.y += this.velocityY * deltaTime;

        // Update trail
        this.updateTrail();

        // Update exhaust particles
        this.updateExhaustParticles(deltaTime);

        // Add exhaust particles when accelerating
        if (this.isAccelerating && this.speed > 10) {
            this.addExhaustParticle();
        }

        // Keep vehicle on screen (with some tolerance)
        this.constrainToScreen();
    }

    updateTrail() {
        if (this.speed > 5) {
            this.trail.push({
                x: this.x,
                y: this.y,
                alpha: 1.0
            });

            if (this.trail.length > this.maxTrailLength) {
                this.trail.shift();
            }
        }

        // Fade trail
        for (let i = 0; i < this.trail.length; i++) {
            this.trail[i].alpha -= 0.05;
        }

        this.trail = this.trail.filter(point => point.alpha > 0);
    }

    addExhaustParticle() {
        if (this.exhaustParticles.length >= this.maxExhaustParticles) {
            this.exhaustParticles.shift();
        }

        // Calculate exhaust position (rear of car)
        const angleRad = this.angle * Math.PI / 180;
        const exhaustX = this.x - Math.sin(angleRad) * this.height * 0.4;
        const exhaustY = this.y + Math.cos(angleRad) * this.height * 0.4;

        this.exhaustParticles.push({
            x: exhaustX + (Math.random() - 0.5) * 10,
            y: exhaustY + (Math.random() - 0.5) * 5,
            vx: (Math.random() - 0.5) * 20 - this.velocityX * 0.1,
            vy: (Math.random() - 0.5) * 20 - this.velocityY * 0.1,
            size: 2 + Math.random() * 4,
            alpha: 0.8,
            life: 1.0
        });
    }

    updateExhaustParticles(deltaTime) {
        this.exhaustParticles.forEach(particle => {
            particle.x += particle.vx * deltaTime;
            particle.y += particle.vy * deltaTime;
            particle.life -= deltaTime * 2;
            particle.alpha = particle.life * 0.8;
            particle.size *= 0.98;
        });

        this.exhaustParticles = this.exhaustParticles.filter(particle => particle.life > 0);
    }

    constrainToScreen() {
        const canvas = document.getElementById('gameCanvas');

        // Road constraints - keep car on road only
        const roadWidth = 200;
        const roadX = canvas.width / 2 - roadWidth / 2;
        const roadLeftBoundary = roadX + 20; // Left edge of road + margin
        const roadRightBoundary = roadX + roadWidth - 20; // Right edge of road - margin

        // Check if car is near road boundaries for warning
        const warningZone = 30;
        this.nearLeftBoundary = this.x < roadLeftBoundary + warningZone;
        this.nearRightBoundary = this.x > roadRightBoundary - warningZone;

        // Constrain horizontally to road boundaries
        if (this.x < roadLeftBoundary) {
            this.x = roadLeftBoundary;
            this.velocityX = Math.max(0, this.velocityX);
            this.offRoadWarning = true;
        } else if (this.x > roadRightBoundary) {
            this.x = roadRightBoundary;
            this.velocityX = Math.min(0, this.velocityX);
            this.offRoadWarning = true;
        } else {
            this.offRoadWarning = false;
        }

        // Allow vertical movement along the road (no Y constraints for road travel)
        // Only prevent going too far off screen vertically
        const verticalMargin = 100;
        if (this.y < -canvas.height * 10) { // Allow going very far up (for long distances)
            this.y = -canvas.height * 10;
        }
        if (this.y > canvas.height + verticalMargin) {
            this.y = canvas.height + verticalMargin;
        }
    }

    handleCollision(collision) {
        // Reduce speed on collision
        this.speed *= 0.5;

        // Add damage
        this.damageLevel += collision.damage || 1;
        this.damaged = this.damageLevel > 0;

        // Bounce effect
        if (collision.normal) {
            const dot = this.velocityX * collision.normal.x + this.velocityY * collision.normal.y;
            this.velocityX -= 2 * dot * collision.normal.x * 0.5;
            this.velocityY -= 2 * dot * collision.normal.y * 0.5;
        }
    }

    handleBoundaryCollision() {
        this.speed *= 0.8;
    }

    reset(x, y) {
        // Always position car in the center of the road
        const canvas = document.getElementById('gameCanvas');
        const roadWidth = 200;
        const roadX = canvas ? canvas.width / 2 - roadWidth / 2 : 300;

        this.x = roadX + roadWidth / 2; // Center of road
        this.y = y;
        this.speed = 0;
        this.angle = 0;
        this.steeringAngle = 0;
        this.velocityX = 0;
        this.velocityY = 0;
        this.damaged = false;
        this.damageLevel = 0;
        this.trail = [];
        this.isAccelerating = false;
        this.isBraking = false;
        this.currentSteering = 0;
    }

    getBounds() {
        const cos = Math.cos(this.angle * Math.PI / 180);
        const sin = Math.sin(this.angle * Math.PI / 180);

        const corners = [
            { x: -this.width/2, y: -this.height/2 },
            { x: this.width/2, y: -this.height/2 },
            { x: this.width/2, y: this.height/2 },
            { x: -this.width/2, y: this.height/2 }
        ];

        return corners.map(corner => ({
            x: this.x + corner.x * cos - corner.y * sin,
            y: this.y + corner.x * sin + corner.y * cos
        }));
    }

    render(ctx, game = null) {
        // Check if we're in driver's seat view
        const isDriverView = game && game.driverSeatView;

        if (!isDriverView) {
            // Third-person view - render the car normally
            ctx.save();

            // Render trail
            this.renderTrail(ctx);

            // Render exhaust particles
            this.renderExhaustParticles(ctx);

            // Move to vehicle position and rotate
            ctx.translate(this.x, this.y);
            ctx.rotate(this.angle * Math.PI / 180);

            // Render 3D car
            this.render3DCar(ctx);

            ctx.restore();
        } else {
            // Driver's seat view - don't render the car body, but render dashboard
            this.renderDriverDashboard(ctx, game);
        }

        // Render speed indicator
        this.renderSpeedIndicator(ctx);

        // Render road boundary warnings
        this.renderRoadWarnings(ctx);
    }

    render3DCar(ctx) {
        // 3D Car rendering with multiple layers for depth

        // 1. Car shadow (ground level)
        ctx.save();
        ctx.translate(4, 6);
        ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
        ctx.fillRect(-this.width/2, -this.height/2, this.width, this.height);
        ctx.restore();

        // 2. Wheels (bottom layer)
        this.render3DWheels(ctx);

        // 3. Car undercarriage
        ctx.fillStyle = '#2C2C2C';
        ctx.fillRect(-this.width/2 + 2, -this.height/2 + 2, this.width - 4, this.height - 4);

        // 4. Main car body (3D effect)
        this.render3DBody(ctx);

        // 5. Car roof and cabin
        this.render3DRoof(ctx);

        // 6. Windows and glass
        this.render3DWindows(ctx);

        // 7. Headlights and taillights
        this.render3DLights(ctx);

        // 8. Car details and trim
        this.render3DDetails(ctx);

        // 9. Damage effects
        if (this.damaged) {
            this.renderDamage(ctx);
        }
    }

    render3DBody(ctx) {
        // BMW body with authentic styling
        const bodyGradient = ctx.createLinearGradient(-this.width/2, -this.height/2, this.width/2, 0);
        if (this.damaged) {
            bodyGradient.addColorStop(0, '#A0522D');
            bodyGradient.addColorStop(0.3, '#8B4513');
            bodyGradient.addColorStop(0.7, '#654321');
            bodyGradient.addColorStop(1, '#4A4A4A');
        } else {
            // BMW Metallic Paint
            bodyGradient.addColorStop(0, '#4A90E2');
            bodyGradient.addColorStop(0.2, this.color);
            bodyGradient.addColorStop(0.6, '#1A252F');
            bodyGradient.addColorStop(1, '#0F1419');
        }

        ctx.fillStyle = bodyGradient;
        ctx.fillRect(-this.width/2, -this.height/2, this.width, this.height);

        // BMW characteristic side highlight
        const highlightGradient = ctx.createLinearGradient(-this.width/2, 0, -this.width/4, 0);
        highlightGradient.addColorStop(0, this.damaged ? '#CD853F' : '#E8F4FD');
        highlightGradient.addColorStop(1, 'rgba(232, 244, 253, 0)');

        ctx.fillStyle = highlightGradient;
        ctx.fillRect(-this.width/2, -this.height/2, this.width/3, this.height);

        // BMW body lines and character lines
        ctx.strokeStyle = '#1A1A1A';
        ctx.lineWidth = 2;
        ctx.strokeRect(-this.width/2, -this.height/2, this.width, this.height);

        // BMW characteristic shoulder line
        ctx.strokeStyle = '#4A90E2';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(-this.width/2 + 5, -this.height/4);
        ctx.lineTo(this.width/2 - 5, -this.height/4);
        ctx.stroke();

        // Side panels with BMW styling
        ctx.strokeStyle = '#666666';
        ctx.lineWidth = 1;
        ctx.strokeRect(-this.width/2 + 3, -this.height/2 + 8, this.width - 6, this.height - 16);

        // BMW door separation lines
        ctx.strokeStyle = '#2C3E50';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(-this.width/2 + 8, -this.height/2 + 10);
        ctx.lineTo(-this.width/2 + 8, this.height/2 - 10);
        ctx.moveTo(this.width/2 - 8, -this.height/2 + 10);
        ctx.lineTo(this.width/2 - 8, this.height/2 - 10);
        ctx.stroke();
    }

    render3DRoof(ctx) {
        // BMW roof with sleek design
        const roofWidth = this.width * 0.75;
        const roofHeight = this.height * 0.55;

        // BMW roof gradient
        const roofGradient = ctx.createLinearGradient(0, -roofHeight/2, 0, roofHeight/2);
        if (this.damaged) {
            roofGradient.addColorStop(0, '#8B4513');
            roofGradient.addColorStop(0.5, '#654321');
            roofGradient.addColorStop(1, '#4A4A4A');
        } else {
            roofGradient.addColorStop(0, '#3A5998');
            roofGradient.addColorStop(0.3, this.color);
            roofGradient.addColorStop(0.7, '#1A252F');
            roofGradient.addColorStop(1, '#0F1419');
        }

        ctx.fillStyle = roofGradient;
        ctx.fillRect(-roofWidth/2, -roofHeight/2, roofWidth, roofHeight);

        // BMW roof highlight
        ctx.fillStyle = this.damaged ? '#A0522D' : '#E8F4FD';
        ctx.fillRect(-roofWidth/2, -roofHeight/2, roofWidth/4, roofHeight);

        // Roof outline
        ctx.strokeStyle = '#1A1A1A';
        ctx.lineWidth = 1.5;
        ctx.strokeRect(-roofWidth/2, -roofHeight/2, roofWidth, roofHeight);

        // BMW sunroof (optional)
        if (!this.damaged) {
            ctx.fillStyle = '#2C3E50';
            ctx.fillRect(-roofWidth/3, -roofHeight/3, roofWidth * 2/3, roofHeight * 2/3);
            ctx.strokeStyle = '#4A90E2';
            ctx.lineWidth = 1;
            ctx.strokeRect(-roofWidth/3, -roofHeight/3, roofWidth * 2/3, roofHeight * 2/3);
        }
    }

    render3DWindows(ctx) {
        // Front windshield with 3D effect
        const windshieldGradient = ctx.createLinearGradient(0, -this.height/2 + 8, 0, -this.height/2 + 25);
        windshieldGradient.addColorStop(0, '#E3F2FD');
        windshieldGradient.addColorStop(0.3, '#BBDEFB');
        windshieldGradient.addColorStop(0.7, '#90CAF9');
        windshieldGradient.addColorStop(1, '#64B5F6');

        ctx.fillStyle = windshieldGradient;
        ctx.fillRect(-this.width/3, -this.height/2 + 8, this.width * 2/3, 17);

        // Windshield reflection
        ctx.fillStyle = 'rgba(255, 255, 255, 0.4)';
        ctx.fillRect(-this.width/3, -this.height/2 + 8, this.width/4, 17);

        // Windshield frame
        ctx.strokeStyle = '#2C2C2C';
        ctx.lineWidth = 2;
        ctx.strokeRect(-this.width/3, -this.height/2 + 8, this.width * 2/3, 17);

        // Rear windshield
        ctx.fillStyle = windshieldGradient;
        ctx.fillRect(-this.width/3, this.height/2 - 25, this.width * 2/3, 17);

        // Rear windshield reflection
        ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
        ctx.fillRect(-this.width/3, this.height/2 - 25, this.width/4, 17);

        // Rear windshield frame
        ctx.strokeStyle = '#2C2C2C';
        ctx.lineWidth = 2;
        ctx.strokeRect(-this.width/3, this.height/2 - 25, this.width * 2/3, 17);

        // Side windows
        ctx.fillStyle = windshieldGradient;
        ctx.fillRect(-this.width/2 + 2, -this.height/4, 8, this.height/2);
        ctx.fillRect(this.width/2 - 10, -this.height/4, 8, this.height/2);

        // Side window frames
        ctx.strokeStyle = '#2C2C2C';
        ctx.lineWidth = 1;
        ctx.strokeRect(-this.width/2 + 2, -this.height/4, 8, this.height/2);
        ctx.strokeRect(this.width/2 - 10, -this.height/4, 8, this.height/2);
    }

    render3DLights(ctx) {
        // BMW Angel Eyes Headlights
        const headlightGradient = ctx.createRadialGradient(0, 0, 0, 0, 0, 7);
        headlightGradient.addColorStop(0, '#FFFFFF');
        headlightGradient.addColorStop(0.4, '#F0F8FF');
        headlightGradient.addColorStop(0.8, '#E6E6FA');
        headlightGradient.addColorStop(1, '#D3D3D3');

        // Left BMW headlight
        ctx.save();
        ctx.translate(-this.width/2 + 10, -this.height/2 + 6);

        // Headlight housing
        ctx.fillStyle = '#2C2C2C';
        ctx.fillRect(-8, -4, 16, 8);

        // Main headlight
        ctx.fillStyle = headlightGradient;
        ctx.beginPath();
        ctx.arc(0, 0, 6, 0, Math.PI * 2);
        ctx.fill();

        // BMW Angel Eye ring
        ctx.strokeStyle = '#4A90E2';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.arc(0, 0, 5, 0, Math.PI * 2);
        ctx.stroke();

        // Inner light
        ctx.fillStyle = '#FFFFFF';
        ctx.beginPath();
        ctx.arc(0, 0, 3, 0, Math.PI * 2);
        ctx.fill();

        // Headlight reflection
        ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        ctx.beginPath();
        ctx.arc(-2, -2, 2, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();

        // Right BMW headlight
        ctx.save();
        ctx.translate(this.width/2 - 10, -this.height/2 + 6);

        // Headlight housing
        ctx.fillStyle = '#2C2C2C';
        ctx.fillRect(-8, -4, 16, 8);

        // Main headlight
        ctx.fillStyle = headlightGradient;
        ctx.beginPath();
        ctx.arc(0, 0, 6, 0, Math.PI * 2);
        ctx.fill();

        // BMW Angel Eye ring
        ctx.strokeStyle = '#4A90E2';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.arc(0, 0, 5, 0, Math.PI * 2);
        ctx.stroke();

        // Inner light
        ctx.fillStyle = '#FFFFFF';
        ctx.beginPath();
        ctx.arc(0, 0, 3, 0, Math.PI * 2);
        ctx.fill();

        // Headlight reflection
        ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        ctx.beginPath();
        ctx.arc(-2, -2, 2, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();

        // Enhanced 3D taillights
        const taillightGradient = ctx.createRadialGradient(0, 0, 0, 0, 0, 4);
        taillightGradient.addColorStop(0, '#FF6B6B');
        taillightGradient.addColorStop(0.6, '#FF5252');
        taillightGradient.addColorStop(1, '#D32F2F');

        // Left taillight
        ctx.save();
        ctx.translate(-this.width/2 + 8, this.height/2 - 4);
        ctx.fillStyle = taillightGradient;
        ctx.beginPath();
        ctx.arc(0, 0, 3, 0, Math.PI * 2);
        ctx.fill();

        ctx.strokeStyle = '#8D1E1E';
        ctx.lineWidth = 1;
        ctx.stroke();
        ctx.restore();

        // Right taillight
        ctx.save();
        ctx.translate(this.width/2 - 8, this.height/2 - 4);
        ctx.fillStyle = taillightGradient;
        ctx.beginPath();
        ctx.arc(0, 0, 3, 0, Math.PI * 2);
        ctx.fill();

        ctx.strokeStyle = '#8D1E1E';
        ctx.lineWidth = 1;
        ctx.stroke();
        ctx.restore();

        // Headlight glow effect when moving
        if (this.speed > 5) {
            ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
            ctx.beginPath();
            ctx.arc(-this.width/2 + 8, -this.height/2 + 4, 12, 0, Math.PI * 2);
            ctx.fill();
            ctx.beginPath();
            ctx.arc(this.width/2 - 8, -this.height/2 + 4, 12, 0, Math.PI * 2);
            ctx.fill();
        }
    }

    render3DWheels(ctx) {
        // Enhanced 3D wheels with detailed rims
        this.render3DWheel(ctx, -this.width/2 - 4, -this.height/3 + 8);
        this.render3DWheel(ctx, this.width/2 + 4, -this.height/3 + 8);
        this.render3DWheel(ctx, -this.width/2 - 4, this.height/3 - 8);
        this.render3DWheel(ctx, this.width/2 + 4, this.height/3 - 8);
    }

    render3DWheel(ctx, x, y) {
        ctx.save();
        ctx.translate(x, y);

        // Wheel shadow
        ctx.fillStyle = 'rgba(0, 0, 0, 0.4)';
        ctx.beginPath();
        ctx.arc(2, 2, 10, 0, Math.PI * 2);
        ctx.fill();

        // BMW Performance tire (outer)
        const tireGradient = ctx.createRadialGradient(0, 0, 0, 0, 0, 9);
        tireGradient.addColorStop(0, '#2C2C2C');
        tireGradient.addColorStop(0.6, '#1A1A1A');
        tireGradient.addColorStop(0.9, '#0D0D0D');
        tireGradient.addColorStop(1, '#000000');

        ctx.fillStyle = tireGradient;
        ctx.beginPath();
        ctx.arc(0, 0, 9, 0, Math.PI * 2);
        ctx.fill();

        // BMW tire tread pattern
        ctx.strokeStyle = '#0D0D0D';
        ctx.lineWidth = 1;
        for (let i = 0; i < 12; i++) {
            const angle = (i / 12) * Math.PI * 2;
            ctx.beginPath();
            ctx.moveTo(Math.cos(angle) * 7, Math.sin(angle) * 7);
            ctx.lineTo(Math.cos(angle) * 9, Math.sin(angle) * 9);
            ctx.stroke();
        }

        // BMW alloy rim
        const rimGradient = ctx.createRadialGradient(-1, -1, 0, 0, 0, 6);
        rimGradient.addColorStop(0, '#F8F8F8');
        rimGradient.addColorStop(0.2, '#E8E8E8');
        rimGradient.addColorStop(0.6, '#D0D0D0');
        rimGradient.addColorStop(0.9, '#B8B8B8');
        rimGradient.addColorStop(1, '#A0A0A0');

        ctx.fillStyle = rimGradient;
        ctx.beginPath();
        ctx.arc(0, 0, 6, 0, Math.PI * 2);
        ctx.fill();

        // BMW M-style spokes (double spoke design)
        ctx.strokeStyle = '#888888';
        ctx.lineWidth = 2;
        for (let i = 0; i < 5; i++) {
            const angle = (i / 5) * Math.PI * 2;
            const angle2 = angle + 0.2;

            // First spoke
            ctx.beginPath();
            ctx.moveTo(Math.cos(angle) * 1, Math.sin(angle) * 1);
            ctx.lineTo(Math.cos(angle) * 5, Math.sin(angle) * 5);
            ctx.stroke();

            // Second spoke (slightly offset)
            ctx.beginPath();
            ctx.moveTo(Math.cos(angle2) * 1, Math.sin(angle2) * 1);
            ctx.lineTo(Math.cos(angle2) * 5, Math.sin(angle2) * 5);
            ctx.stroke();
        }

        // BMW center cap with logo
        ctx.fillStyle = '#FFFFFF';
        ctx.beginPath();
        ctx.arc(0, 0, 2.5, 0, Math.PI * 2);
        ctx.fill();

        // BMW logo on center cap
        ctx.strokeStyle = '#0066CC';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.arc(0, 0, 2, 0, Math.PI * 2);
        ctx.stroke();

        // BMW logo quarters
        ctx.fillStyle = '#0066CC';
        ctx.beginPath();
        ctx.arc(0, 0, 1.5, 0, Math.PI, false);
        ctx.fill();

        // Rim highlight
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        ctx.beginPath();
        ctx.arc(-2, -2, 2, 0, Math.PI * 2);
        ctx.fill();

        // Rim edge highlight
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.4)';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.arc(0, 0, 6, 0, Math.PI * 2);
        ctx.stroke();

        ctx.restore();
    }

    render3DDetails(ctx) {
        // BMW Kidney Grille (iconic)
        ctx.save();
        ctx.translate(0, -this.height/2 + 8);

        // Left kidney
        ctx.fillStyle = '#1A1A1A';
        ctx.beginPath();
        ctx.ellipse(-6, 0, 5, 8, 0, 0, Math.PI * 2);
        ctx.fill();
        ctx.strokeStyle = '#C0C0C0';
        ctx.lineWidth = 2;
        ctx.stroke();

        // Right kidney
        ctx.fillStyle = '#1A1A1A';
        ctx.beginPath();
        ctx.ellipse(6, 0, 5, 8, 0, 0, Math.PI * 2);
        ctx.fill();
        ctx.strokeStyle = '#C0C0C0';
        ctx.lineWidth = 2;
        ctx.stroke();

        // Grille slats
        ctx.strokeStyle = '#666666';
        ctx.lineWidth = 1;
        for (let i = -3; i <= 3; i++) {
            ctx.beginPath();
            ctx.moveTo(-10, i * 2);
            ctx.lineTo(-2, i * 2);
            ctx.moveTo(2, i * 2);
            ctx.lineTo(10, i * 2);
            ctx.stroke();
        }
        ctx.restore();

        // BMW Logo area
        ctx.fillStyle = '#FFFFFF';
        ctx.beginPath();
        ctx.arc(0, -this.height/2 + 8, 4, 0, Math.PI * 2);
        ctx.fill();
        ctx.strokeStyle = '#0066CC';
        ctx.lineWidth = 2;
        ctx.stroke();

        // BMW logo quarters
        ctx.fillStyle = '#0066CC';
        ctx.beginPath();
        ctx.arc(0, -this.height/2 + 8, 3, 0, Math.PI, false);
        ctx.fill();

        // Chrome door handles
        ctx.fillStyle = '#E8E8E8';
        ctx.fillRect(-this.width/2 + 2, -3, 4, 2);
        ctx.fillRect(this.width/2 - 6, -3, 4, 2);

        // Door handle highlights
        ctx.fillStyle = '#FFFFFF';
        ctx.fillRect(-this.width/2 + 2, -3, 1, 2);
        ctx.fillRect(this.width/2 - 6, -3, 1, 2);

        // BMW side mirrors (larger, more detailed)
        ctx.fillStyle = this.color;
        ctx.fillRect(-this.width/2 - 3, -this.height/4, 6, 4);
        ctx.fillRect(this.width/2 - 3, -this.height/4, 6, 4);

        // Mirror glass with BMW blue tint
        ctx.fillStyle = '#4A90E2';
        ctx.fillRect(-this.width/2 - 2, -this.height/4 + 0.5, 4, 3);
        ctx.fillRect(this.width/2 - 2, -this.height/4 + 0.5, 4, 3);

        // Mirror highlights
        ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
        ctx.fillRect(-this.width/2 - 2, -this.height/4 + 0.5, 1, 3);
        ctx.fillRect(this.width/2 - 2, -this.height/4 + 0.5, 1, 3);

        // BMW license plate
        ctx.fillStyle = '#FFFFFF';
        ctx.fillRect(-10, this.height/2 - 10, 20, 8);
        ctx.strokeStyle = '#2C2C2C';
        ctx.lineWidth = 1;
        ctx.strokeRect(-10, this.height/2 - 10, 20, 8);

        // License plate text
        ctx.fillStyle = '#000000';
        ctx.font = '6px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('BMW', 0, this.height/2 - 5);

        // BMW antenna
        ctx.strokeStyle = '#C0C0C0';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(this.width/3, -this.height/2 + 10);
        ctx.lineTo(this.width/3, -this.height/2 - 8);
        ctx.stroke();

        // Side indicator lights
        ctx.fillStyle = '#FFA500';
        ctx.beginPath();
        ctx.arc(-this.width/2 + 2, -this.height/4, 2, 0, Math.PI * 2);
        ctx.fill();
        ctx.beginPath();
        ctx.arc(this.width/2 - 2, -this.height/4, 2, 0, Math.PI * 2);
        ctx.fill();
    }

    renderTrail(ctx) {
        if (this.trail.length < 2) return;

        ctx.strokeStyle = `rgba(255, 215, 0, 0.3)`;
        ctx.lineWidth = 3;
        ctx.lineCap = 'round';

        ctx.beginPath();
        ctx.moveTo(this.trail[0].x, this.trail[0].y);

        for (let i = 1; i < this.trail.length; i++) {
            ctx.globalAlpha = this.trail[i].alpha * 0.5;
            ctx.lineTo(this.trail[i].x, this.trail[i].y);
        }

        ctx.stroke();
        ctx.globalAlpha = 1;
    }



    renderExhaustParticles(ctx) {
        this.exhaustParticles.forEach(particle => {
            ctx.save();
            ctx.globalAlpha = particle.alpha;

            // Create gradient for exhaust particle
            const gradient = ctx.createRadialGradient(
                particle.x, particle.y, 0,
                particle.x, particle.y, particle.size
            );
            gradient.addColorStop(0, 'rgba(100, 100, 100, 0.8)');
            gradient.addColorStop(0.5, 'rgba(80, 80, 80, 0.4)');
            gradient.addColorStop(1, 'rgba(60, 60, 60, 0)');

            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            ctx.fill();

            ctx.restore();
        });
    }





    renderDamage(ctx) {
        // Smoke effect
        ctx.fillStyle = `rgba(100, 100, 100, ${0.3 + Math.random() * 0.3})`;
        for (let i = 0; i < this.damageLevel; i++) {
            const smokeX = (Math.random() - 0.5) * this.width;
            const smokeY = this.height/2 + Math.random() * 10;
            ctx.fillRect(smokeX, smokeY, 3, 3);
        }

        // Damage marks
        ctx.fillStyle = '#654321';
        for (let i = 0; i < this.damageLevel; i++) {
            const markX = (Math.random() - 0.5) * this.width;
            const markY = (Math.random() - 0.5) * this.height;
            ctx.fillRect(markX, markY, 2, 2);
        }
    }

    renderSpeedIndicator(ctx) {
        const canvas = document.getElementById('gameCanvas');
        const x = canvas.width - 150;
        const y = canvas.height - 50;

        // Background
        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        ctx.fillRect(x, y, 120, 30);

        // Border
        ctx.strokeStyle = '#FFD700';
        ctx.lineWidth = 2;
        ctx.strokeRect(x, y, 120, 30);

        // Speed bar
        const speedPercent = Math.abs(this.speed) / this.maxSpeed;
        const barWidth = 100 * speedPercent;

        ctx.fillStyle = this.speed > this.maxSpeed * 0.8 ? '#FF4444' : '#00FF00';
        ctx.fillRect(x + 10, y + 10, barWidth, 10);

        // Speed text
        ctx.fillStyle = '#FFFFFF';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(`${Math.round(Math.abs(this.speed))} km/h`, x + 60, y + 20);
    }

    renderRoadWarnings(ctx) {
        const canvas = document.getElementById('gameCanvas');

        // Show warning when car is at road boundaries
        if (this.offRoadWarning) {
            // Flash red warning
            ctx.save();
            ctx.fillStyle = `rgba(255, 0, 0, ${0.3 + Math.sin(Date.now() * 0.01) * 0.2})`;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Warning text
            ctx.fillStyle = '#FF0000';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('STAY ON ROAD!', canvas.width / 2, 100);
            ctx.restore();
        }

        // Show boundary warnings when near edges
        if (this.nearLeftBoundary || this.nearRightBoundary) {
            ctx.save();
            const alpha = 0.1 + Math.sin(Date.now() * 0.005) * 0.1;

            if (this.nearLeftBoundary) {
                ctx.fillStyle = `rgba(255, 165, 0, ${alpha})`;
                ctx.fillRect(0, 0, 50, canvas.height);
            }

            if (this.nearRightBoundary) {
                ctx.fillStyle = `rgba(255, 165, 0, ${alpha})`;
                ctx.fillRect(canvas.width - 50, 0, 50, canvas.height);
            }

            ctx.restore();
        }
    }

    renderDriverDashboard(ctx, game) {
        const canvas = ctx.canvas;
        const dashHeight = game.dashboardHeight;
        const isRearView = game.currentView === 'rear';

        // Render appropriate windshield frame
        if (isRearView) {
            this.renderRearWindshieldFrame(ctx, canvas);
        } else {
            this.renderRealisticWindshieldFrame(ctx, canvas);
        }

        // BMW Dashboard background (lower portion)
        const dashGradient = ctx.createLinearGradient(0, canvas.height - dashHeight, 0, canvas.height);
        dashGradient.addColorStop(0, 'rgba(26, 26, 26, 0.9)'); // Semi-transparent for realism
        dashGradient.addColorStop(0.3, 'rgba(44, 44, 44, 0.95)');
        dashGradient.addColorStop(0.7, 'rgba(26, 26, 26, 0.98)');
        dashGradient.addColorStop(1, 'rgba(13, 13, 13, 1)');

        ctx.fillStyle = dashGradient;
        ctx.fillRect(0, canvas.height - dashHeight, canvas.width, dashHeight);

        // Dashboard reflection on windshield (subtle)
        this.renderDashboardReflection(ctx, canvas);

        // BMW steering wheel (realistic position)
        this.renderRealisticSteeringWheel(ctx, canvas);

        // BMW instrument cluster (driver's view)
        this.renderDriverInstrumentCluster(ctx, canvas, game);

        // Side mirrors (realistic positioning)
        this.renderRealisticSideMirrors(ctx, canvas);

        // Windshield wipers (subtle)
        if (!isRearView) {
            this.renderWindshieldWipers(ctx, canvas);
        }

        // View indicator
        this.renderViewIndicator(ctx, canvas, game);
    }

    renderSteeringWheel(ctx, canvas) {
        const centerX = canvas.width / 2;
        const centerY = canvas.height - 40;
        const radius = 35;

        // Steering wheel outer ring
        const wheelGradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);
        wheelGradient.addColorStop(0, '#2C2C2C');
        wheelGradient.addColorStop(0.7, '#1A1A1A');
        wheelGradient.addColorStop(1, '#0D0D0D');

        ctx.fillStyle = wheelGradient;
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, Math.PI, 0); // Bottom half only
        ctx.fill();

        // Steering wheel spokes
        ctx.strokeStyle = '#333333';
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.moveTo(centerX - 20, centerY);
        ctx.lineTo(centerX + 20, centerY);
        ctx.moveTo(centerX, centerY - 10);
        ctx.lineTo(centerX, centerY + 10);
        ctx.stroke();

        // BMW logo on steering wheel
        ctx.fillStyle = '#0066CC';
        ctx.beginPath();
        ctx.arc(centerX, centerY, 8, 0, Math.PI * 2);
        ctx.fill();

        ctx.fillStyle = '#FFFFFF';
        ctx.beginPath();
        ctx.arc(centerX, centerY, 6, 0, Math.PI, false);
        ctx.fill();
    }

    renderInstrumentCluster(ctx, canvas) {
        const centerX = canvas.width / 2;
        const dashY = canvas.height - 80;

        // Speedometer (left)
        this.renderSpeedometer(ctx, centerX - 80, dashY);

        // Tachometer (right)
        this.renderTachometer(ctx, centerX + 80, dashY);

        // Digital display (center)
        this.renderDigitalDisplay(ctx, centerX, dashY);
    }

    renderSpeedometer(ctx, x, y) {
        const radius = 25;

        // Gauge background
        ctx.fillStyle = '#0D0D0D';
        ctx.beginPath();
        ctx.arc(x, y, radius, 0, Math.PI * 2);
        ctx.fill();

        // Gauge ring
        ctx.strokeStyle = '#4A90E2';
        ctx.lineWidth = 2;
        ctx.stroke();

        // Speed markings
        ctx.strokeStyle = '#FFFFFF';
        ctx.lineWidth = 1;
        for (let i = 0; i <= 12; i++) {
            const angle = (i / 12) * Math.PI * 2 - Math.PI / 2;
            const x1 = x + Math.cos(angle) * (radius - 5);
            const y1 = y + Math.sin(angle) * (radius - 5);
            const x2 = x + Math.cos(angle) * (radius - 2);
            const y2 = y + Math.sin(angle) * (radius - 2);

            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
        }

        // Speed needle
        const speedAngle = (this.speed / 60) * Math.PI * 1.5 - Math.PI / 2;
        ctx.strokeStyle = '#FF4444';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(x, y);
        ctx.lineTo(x + Math.cos(speedAngle) * (radius - 8), y + Math.sin(speedAngle) * (radius - 8));
        ctx.stroke();

        // Center dot
        ctx.fillStyle = '#FF4444';
        ctx.beginPath();
        ctx.arc(x, y, 2, 0, Math.PI * 2);
        ctx.fill();

        // Speed text
        ctx.fillStyle = '#FFFFFF';
        ctx.font = '8px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('KM/H', x, y + 15);
    }

    renderTachometer(ctx, x, y) {
        const radius = 25;

        // Gauge background
        ctx.fillStyle = '#0D0D0D';
        ctx.beginPath();
        ctx.arc(x, y, radius, 0, Math.PI * 2);
        ctx.fill();

        // Gauge ring
        ctx.strokeStyle = '#4A90E2';
        ctx.lineWidth = 2;
        ctx.stroke();

        // RPM markings
        ctx.strokeStyle = '#FFFFFF';
        ctx.lineWidth = 1;
        for (let i = 0; i <= 8; i++) {
            const angle = (i / 8) * Math.PI * 1.5 - Math.PI / 2;
            const x1 = x + Math.cos(angle) * (radius - 5);
            const y1 = y + Math.sin(angle) * (radius - 5);
            const x2 = x + Math.cos(angle) * (radius - 2);
            const y2 = y + Math.sin(angle) * (radius - 2);

            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
        }

        // RPM needle (based on speed)
        const rpm = (this.speed / 60) * 6000; // Simulate RPM
        const rpmAngle = (rpm / 6000) * Math.PI * 1.5 - Math.PI / 2;
        ctx.strokeStyle = '#FF4444';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(x, y);
        ctx.lineTo(x + Math.cos(rpmAngle) * (radius - 8), y + Math.sin(rpmAngle) * (radius - 8));
        ctx.stroke();

        // Center dot
        ctx.fillStyle = '#FF4444';
        ctx.beginPath();
        ctx.arc(x, y, 2, 0, Math.PI * 2);
        ctx.fill();

        // RPM text
        ctx.fillStyle = '#FFFFFF';
        ctx.font = '8px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('RPM', x, y + 15);
    }

    renderDigitalDisplay(ctx, x, y) {
        // Digital display background
        ctx.fillStyle = '#000000';
        ctx.fillRect(x - 40, y - 15, 80, 30);

        // Display border
        ctx.strokeStyle = '#4A90E2';
        ctx.lineWidth = 1;
        ctx.strokeRect(x - 40, y - 15, 80, 30);

        // Gear display
        ctx.fillStyle = '#00FF00';
        ctx.font = 'bold 16px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(this.currentGear, x - 20, y + 5);

        // Speed display
        ctx.fillStyle = '#FFFFFF';
        ctx.font = '12px Arial';
        ctx.fillText(`${Math.round(this.speed)} KM/H`, x + 15, y + 5);
    }

    renderCenterConsole(ctx, canvas) {
        const centerX = canvas.width / 2;
        const consoleY = canvas.height - 60;

        // Console background
        ctx.fillStyle = '#1A1A1A';
        ctx.fillRect(centerX - 60, consoleY, 120, 40);

        // BMW iDrive controller
        ctx.fillStyle = '#333333';
        ctx.beginPath();
        ctx.arc(centerX, consoleY + 20, 8, 0, Math.PI * 2);
        ctx.fill();

        // Climate controls
        ctx.fillStyle = '#4A90E2';
        ctx.fillRect(centerX - 50, consoleY + 5, 15, 8);
        ctx.fillRect(centerX - 30, consoleY + 5, 15, 8);
        ctx.fillRect(centerX + 15, consoleY + 5, 15, 8);
        ctx.fillRect(centerX + 35, consoleY + 5, 15, 8);
    }

    renderSideMirrors(ctx, canvas) {
        // Left mirror
        ctx.fillStyle = '#87CEEB';
        ctx.fillRect(10, canvas.height - 100, 60, 40);
        ctx.strokeStyle = '#2C2C2C';
        ctx.lineWidth = 2;
        ctx.strokeRect(10, canvas.height - 100, 60, 40);

        // Right mirror
        ctx.fillStyle = '#87CEEB';
        ctx.fillRect(canvas.width - 70, canvas.height - 100, 60, 40);
        ctx.strokeStyle = '#2C2C2C';
        ctx.lineWidth = 2;
        ctx.strokeRect(canvas.width - 70, canvas.height - 100, 60, 40);

        // Mirror labels
        ctx.fillStyle = '#FFFFFF';
        ctx.font = '10px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('LEFT', 40, canvas.height - 75);
        ctx.fillText('RIGHT', canvas.width - 40, canvas.height - 75);
    }

    renderWindshieldFrame(ctx, canvas) {
        // A-pillars (windshield frame)
        ctx.fillStyle = '#1A1A1A';
        ctx.fillRect(0, 0, 20, canvas.height - 120);
        ctx.fillRect(canvas.width - 20, 0, 20, canvas.height - 120);

        // Windshield top frame
        ctx.fillStyle = '#1A1A1A';
        ctx.fillRect(0, 0, canvas.width, 15);

        // Rearview mirror
        ctx.fillStyle = '#2C2C2C';
        ctx.fillRect(canvas.width / 2 - 15, 15, 30, 8);
        ctx.fillStyle = '#87CEEB';
        ctx.fillRect(canvas.width / 2 - 12, 17, 24, 4);
    }

    // New realistic rendering methods for authentic driver view
    renderRealisticWindshieldFrame(ctx, canvas) {
        // Dr. Driving style - ultra minimal frame for maximum road view

        // Very thin A-pillars (almost invisible like Dr. Driving)
        const pillarWidth = 8;
        ctx.fillStyle = 'rgba(26, 26, 26, 0.7)';
        ctx.fillRect(0, 0, pillarWidth, canvas.height * 0.6);
        ctx.fillRect(canvas.width - pillarWidth, 0, pillarWidth, canvas.height * 0.6);

        // Minimal top frame
        ctx.fillStyle = 'rgba(26, 26, 26, 0.6)';
        ctx.fillRect(0, 0, canvas.width, 4);

        // Small rearview mirror (Dr. Driving style)
        const mirrorWidth = 30;
        const mirrorHeight = 4;
        const mirrorX = canvas.width / 2 - mirrorWidth / 2;

        ctx.fillStyle = 'rgba(44, 44, 44, 0.8)';
        ctx.fillRect(mirrorX, 4, mirrorWidth, mirrorHeight);

        // Mirror glass
        ctx.fillStyle = 'rgba(135, 206, 235, 0.6)';
        ctx.fillRect(mirrorX + 1, 5, mirrorWidth - 2, mirrorHeight - 2);
    }

    renderDashboardReflection(ctx, canvas) {
        // Subtle dashboard reflection on windshield for realism
        const reflectionGradient = ctx.createLinearGradient(0, 0, 0, canvas.height * 0.3);
        reflectionGradient.addColorStop(0, 'rgba(26, 26, 26, 0.05)');
        reflectionGradient.addColorStop(0.5, 'rgba(44, 44, 44, 0.03)');
        reflectionGradient.addColorStop(1, 'rgba(26, 26, 26, 0)');

        ctx.fillStyle = reflectionGradient;
        ctx.fillRect(15, 8, canvas.width - 30, canvas.height * 0.3);
    }

    renderRealisticSteeringWheel(ctx, canvas) {
        const centerX = canvas.width / 2;
        const centerY = canvas.height - 25; // Dr. Driving style position
        const radius = 35; // Smaller like Dr. Driving

        // Steering wheel rotation based on vehicle steering
        const wheelRotation = this.steeringAngle * 1.0; // Direct steering ratio like Dr. Driving

        ctx.save();
        ctx.translate(centerX, centerY);
        ctx.rotate(wheelRotation * Math.PI / 180);

        // Dr. Driving style steering wheel (minimal, only bottom visible)
        const rimGradient = ctx.createRadialGradient(0, 0, 0, 0, 0, radius);
        rimGradient.addColorStop(0, 'rgba(60, 60, 60, 0.8)');
        rimGradient.addColorStop(0.7, 'rgba(44, 44, 44, 0.9)');
        rimGradient.addColorStop(1, 'rgba(26, 26, 26, 1)');

        ctx.strokeStyle = rimGradient.createPattern ? '#3C3C3C' : '#3C3C3C';
        ctx.lineWidth = 6;
        ctx.beginPath();
        ctx.arc(0, 0, radius, Math.PI * 0.4, Math.PI * 0.6); // Minimal bottom arc
        ctx.stroke();

        // Minimal spokes
        ctx.strokeStyle = 'rgba(51, 51, 51, 0.8)';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(-15, 0);
        ctx.lineTo(15, 0);
        ctx.stroke();

        // Small BMW logo
        ctx.fillStyle = 'rgba(0, 102, 204, 0.9)';
        ctx.beginPath();
        ctx.arc(0, 0, 4, 0, Math.PI * 2);
        ctx.fill();

        ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        ctx.beginPath();
        ctx.arc(0, 0, 3, 0, Math.PI, false);
        ctx.fill();

        ctx.restore();
    }

    renderDriverInstrumentCluster(ctx, canvas) {
        // Instrument cluster positioned realistically behind steering wheel
        const clusterX = canvas.width / 2;
        const clusterY = canvas.height - 70;

        // Speedometer (left side, partially visible)
        this.renderCompactSpeedometer(ctx, clusterX - 60, clusterY);

        // Gear and speed display (center, clearly visible)
        this.renderCenterDisplay(ctx, clusterX, clusterY);

        // Tachometer (right side, partially visible)
        this.renderCompactTachometer(ctx, clusterX + 60, clusterY);
    }

    renderCompactSpeedometer(ctx, x, y) {
        const radius = 20;

        // Gauge background (semi-transparent)
        ctx.fillStyle = 'rgba(13, 13, 13, 0.8)';
        ctx.beginPath();
        ctx.arc(x, y, radius, 0, Math.PI * 2);
        ctx.fill();

        // Gauge ring
        ctx.strokeStyle = '#4A90E2';
        ctx.lineWidth = 1;
        ctx.stroke();

        // Speed needle
        const speedAngle = (this.speed / 60) * Math.PI * 1.5 - Math.PI / 2;
        ctx.strokeStyle = '#FF4444';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(x, y);
        ctx.lineTo(x + Math.cos(speedAngle) * (radius - 5), y + Math.sin(speedAngle) * (radius - 5));
        ctx.stroke();

        // Speed text (small)
        ctx.fillStyle = '#FFFFFF';
        ctx.font = '8px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(`${Math.round(this.speed)}`, x, y + 3);
    }

    renderCenterDisplay(ctx, x, y) {
        // Main display - clearly visible to driver
        const displayWidth = 80;
        const displayHeight = 25;

        // Display background
        ctx.fillStyle = 'rgba(0, 0, 0, 0.9)';
        ctx.fillRect(x - displayWidth/2, y - displayHeight/2, displayWidth, displayHeight);

        // Display border
        ctx.strokeStyle = '#4A90E2';
        ctx.lineWidth = 1;
        ctx.strokeRect(x - displayWidth/2, y - displayHeight/2, displayWidth, displayHeight);

        // Gear display (prominent)
        ctx.fillStyle = '#00FF00';
        ctx.font = 'bold 14px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(this.currentGear, x - 20, y + 2);

        // Speed display
        ctx.fillStyle = '#FFFFFF';
        ctx.font = '10px Arial';
        ctx.fillText(`${Math.round(this.speed)} KM/H`, x + 20, y + 2);
    }

    renderCompactTachometer(ctx, x, y) {
        const radius = 20;

        // Gauge background (semi-transparent)
        ctx.fillStyle = 'rgba(13, 13, 13, 0.8)';
        ctx.beginPath();
        ctx.arc(x, y, radius, 0, Math.PI * 2);
        ctx.fill();

        // Gauge ring
        ctx.strokeStyle = '#4A90E2';
        ctx.lineWidth = 1;
        ctx.stroke();

        // RPM needle
        const rpm = (this.speed / 60) * 6000;
        const rpmAngle = (rpm / 6000) * Math.PI * 1.5 - Math.PI / 2;
        ctx.strokeStyle = '#FF4444';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(x, y);
        ctx.lineTo(x + Math.cos(rpmAngle) * (radius - 5), y + Math.sin(rpmAngle) * (radius - 5));
        ctx.stroke();

        // RPM text (small)
        ctx.fillStyle = '#FFFFFF';
        ctx.font = '6px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('RPM', x, y + 12);
    }

    renderRealisticSideMirrors(ctx, canvas) {
        // Dr. Driving style mirrors - smaller and less obtrusive
        const leftMirrorX = 15;
        const leftMirrorY = canvas.height * 0.35;
        const mirrorWidth = 35;
        const mirrorHeight = 20;

        // Left mirror housing (semi-transparent)
        ctx.fillStyle = `rgba(44, 62, 80, 0.8)`;
        ctx.fillRect(leftMirrorX, leftMirrorY, mirrorWidth, mirrorHeight);

        // Left mirror glass
        ctx.fillStyle = 'rgba(135, 206, 235, 0.7)';
        ctx.fillRect(leftMirrorX + 2, leftMirrorY + 2, mirrorWidth - 4, mirrorHeight - 4);

        // Left mirror reflection
        ctx.fillStyle = 'rgba(255, 255, 255, 0.15)';
        ctx.fillRect(leftMirrorX + 2, leftMirrorY + 2, (mirrorWidth - 4) / 3, mirrorHeight - 4);

        // Right mirror (Dr. Driving style)
        const rightMirrorX = canvas.width - 50;
        const rightMirrorY = canvas.height * 0.35;

        // Right mirror housing (semi-transparent)
        ctx.fillStyle = `rgba(44, 62, 80, 0.8)`;
        ctx.fillRect(rightMirrorX, rightMirrorY, mirrorWidth, mirrorHeight);

        // Right mirror glass
        ctx.fillStyle = 'rgba(135, 206, 235, 0.7)';
        ctx.fillRect(rightMirrorX + 2, rightMirrorY + 2, mirrorWidth - 4, mirrorHeight - 4);

        // Right mirror reflection
        ctx.fillStyle = 'rgba(255, 255, 255, 0.15)';
        ctx.fillRect(rightMirrorX + 2, rightMirrorY + 2, (mirrorWidth - 4) / 3, mirrorHeight - 4);
    }

    renderWindshieldWipers(ctx, canvas) {
        // Subtle windshield wipers for realism (parked position)
        ctx.strokeStyle = 'rgba(44, 44, 44, 0.6)';
        ctx.lineWidth = 2;

        // Left wiper
        ctx.beginPath();
        ctx.moveTo(canvas.width * 0.3, canvas.height * 0.8);
        ctx.lineTo(canvas.width * 0.15, canvas.height * 0.2);
        ctx.stroke();

        // Right wiper
        ctx.beginPath();
        ctx.moveTo(canvas.width * 0.7, canvas.height * 0.8);
        ctx.lineTo(canvas.width * 0.85, canvas.height * 0.2);
        ctx.stroke();
    }

    renderRearWindshieldFrame(ctx, canvas) {
        // Rear windshield frame for rear view

        // C-pillars (rear windshield frame)
        const pillarWidth = 20;
        ctx.fillStyle = 'rgba(26, 26, 26, 0.95)';
        ctx.fillRect(0, 0, pillarWidth, canvas.height * 0.7);
        ctx.fillRect(canvas.width - pillarWidth, 0, pillarWidth, canvas.height * 0.7);

        // Top rear windshield frame
        ctx.fillStyle = 'rgba(26, 26, 26, 0.9)';
        ctx.fillRect(0, 0, canvas.width, 10);

        // Rear windshield is typically larger and more angled
        // Add rear windshield defroster lines
        ctx.strokeStyle = 'rgba(100, 100, 100, 0.3)';
        ctx.lineWidth = 1;
        for (let i = 1; i < 8; i++) {
            const y = canvas.height * 0.15 + (i * canvas.height * 0.08);
            ctx.beginPath();
            ctx.moveTo(pillarWidth + 10, y);
            ctx.lineTo(canvas.width - pillarWidth - 10, y);
            ctx.stroke();
        }

        // Rear view mirror (center mounted)
        const mirrorWidth = 35;
        const mirrorHeight = 6;
        const mirrorX = canvas.width / 2 - mirrorWidth / 2;

        ctx.fillStyle = '#2C2C2C';
        ctx.fillRect(mirrorX, 10, mirrorWidth, mirrorHeight);

        // Mirror glass
        ctx.fillStyle = '#87CEEB';
        ctx.fillRect(mirrorX + 2, 12, mirrorWidth - 4, mirrorHeight - 4);
    }

    renderViewIndicator(ctx, canvas, game) {
        // Dr. Driving style view indicator - minimal and clean
        const indicatorX = canvas.width - 70;
        const indicatorY = 15;

        // Minimal background
        ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
        ctx.fillRect(indicatorX, indicatorY, 60, 20);

        // Subtle border
        ctx.strokeStyle = 'rgba(74, 144, 226, 0.8)';
        ctx.lineWidth = 1;
        ctx.strokeRect(indicatorX, indicatorY, 60, 20);

        // View text (Dr. Driving style)
        ctx.fillStyle = game.currentView === 'front' ? '#4A90E2' : '#FF6B6B';
        ctx.font = '10px Arial';
        ctx.textAlign = 'center';
        const viewText = game.currentView === 'front' ? 'FRONT' : 'REAR';
        ctx.fillText(viewText, indicatorX + 30, indicatorY + 13);

        // Minimal switch instruction
        ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
        ctx.font = '6px Arial';
        ctx.fillText('V', indicatorX + 30, indicatorY + 30);
    }

    renderDriverInstrumentCluster(ctx, canvas, game) {
        // Instrument cluster positioned realistically behind steering wheel
        const clusterX = canvas.width / 2;
        const clusterY = canvas.height - 70;
        const isRearView = game && game.currentView === 'rear';

        // Speedometer (left side, partially visible)
        this.renderCompactSpeedometer(ctx, clusterX - 60, clusterY);

        // Gear and speed display (center, clearly visible)
        this.renderCenterDisplay(ctx, clusterX, clusterY, isRearView);

        // Tachometer (right side, partially visible)
        this.renderCompactTachometer(ctx, clusterX + 60, clusterY);
    }

    renderCenterDisplay(ctx, x, y, isRearView = false) {
        // Main display - clearly visible to driver
        const displayWidth = 100;
        const displayHeight = 30;

        // Display background
        ctx.fillStyle = 'rgba(0, 0, 0, 0.9)';
        ctx.fillRect(x - displayWidth/2, y - displayHeight/2, displayWidth, displayHeight);

        // Display border
        ctx.strokeStyle = '#4A90E2';
        ctx.lineWidth = 1;
        ctx.strokeRect(x - displayWidth/2, y - displayHeight/2, displayWidth, displayHeight);

        // Gear display (prominent)
        ctx.fillStyle = '#00FF00';
        ctx.font = 'bold 14px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(this.currentGear, x - 30, y - 2);

        // Speed display
        ctx.fillStyle = '#FFFFFF';
        ctx.font = '10px Arial';
        ctx.fillText(`${Math.round(this.speed)} KM/H`, x + 10, y - 2);

        // View indicator in display
        ctx.fillStyle = isRearView ? '#FF6B6B' : '#4A90E2';
        ctx.font = '8px Arial';
        ctx.fillText(isRearView ? 'REAR' : 'FRONT', x, y + 8);
    }
}
