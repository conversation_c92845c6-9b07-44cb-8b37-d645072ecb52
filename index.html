<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Village to World - Car Game</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div id="gameContainer">
        <div id="gameHeader">
            <div id="levelInfo">
                <span id="currentLevel">Level 1: Village</span>
                <span id="score">Score: 0</span>
            </div>
            <div id="gameStats">
                <span id="speed">Speed: 0 km/h</span>
                <span id="distance">Distance: 0 km</span>
            </div>
        </div>

        <canvas id="gameCanvas" width="800" height="600"></canvas>

        <div id="gameControls">
            <div id="pedals">
                <button id="accelerator" class="pedal accelerator">GAS</button>
                <button id="brake" class="pedal brake">BRAKE</button>
            </div>
            <div id="steeringWheel">
                <div id="wheel">
                    <div id="wheelCenter"></div>
                </div>
            </div>
        </div>

        <div id="gameUI">
            <div id="dashboard">
                <div id="speedometer">
                    <div id="speedNeedle"></div>
                    <div id="speedText">0</div>
                    <div id="speedUnit">km/h</div>
                </div>
                <div id="gearBox">
                    <div id="gearDisplay">
                        <div id="gearText">P</div>
                        <div id="gearLabel">PARK</div>
                    </div>
                    <div id="gearSelector">
                        <button id="gearP" class="gear-btn active">P</button>
                        <button id="gearR" class="gear-btn">R</button>
                        <button id="gearN" class="gear-btn">N</button>
                        <button id="gearD" class="gear-btn">D</button>
                    </div>
                </div>
            </div>
            <div id="miniMap"></div>
            <div id="objective">
                <h3>Objective</h3>
                <p id="currentObjective">Drive safely to the market in the village center</p>
                <div id="progressBar">
                    <div id="progress"></div>
                </div>
            </div>
        </div>
    </div>

    <div id="startScreen" class="screen">
        <h1>Village to World</h1>
        <p>Drive safely from village to the most developed countries!</p>
        <button id="startGame">Start Journey</button>
    </div>

    <div id="levelComplete" class="screen hidden">
        <h2>Level Complete!</h2>
        <p id="levelCompleteText"></p>
        <button id="nextLevel">Continue Journey</button>
    </div>

    <div id="gameOver" class="screen hidden">
        <h2>Journey Failed</h2>
        <p id="gameOverText"></p>
        <button id="restartGame">Try Again</button>
    </div>

    <script src="js/vehicle.js"></script>
    <script src="js/environment.js"></script>
    <script src="js/levels.js"></script>
    <script src="js/ui.js"></script>
    <script src="js/audio.js"></script>
    <script src="js/game.js"></script>
</body>
</html>
