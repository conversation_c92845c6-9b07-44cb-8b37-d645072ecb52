// Environment and World Rendering

class Environment {
    constructor() {
        this.currentLevel = 1;
        this.scrollY = 0;
        this.scrollSpeed = 0;

        // Environment elements
        this.roads = [];
        this.buildings = [];
        this.trees = [];
        this.obstacles = [];
        this.decorations = [];

        // Level-specific properties
        this.levelConfigs = {
            1: { // Village
                name: 'Village',
                backgroundColor: '#87CEEB',
                roadColor: '#8B4513',
                grassColor: '#228B22',
                treeCount: 50,
                buildingCount: 8,
                obstacleCount: 5
            },
            2: { // Mandal
                name: 'Mandal',
                backgroundColor: '#87CEEB',
                roadColor: '#696969',
                grassColor: '#32CD32',
                treeCount: 35,
                buildingCount: 15,
                obstacleCount: 8
            },
            3: { // District
                name: 'District',
                backgroundColor: '#B0C4DE',
                roadColor: '#2F4F4F',
                grassColor: '#90EE90',
                treeCount: 25,
                buildingCount: 25,
                obstacleCount: 12
            },
            4: { // Major City
                name: 'Major City',
                backgroundColor: '#D3D3D3',
                roadColor: '#2F2F2F',
                grassColor: '#98FB98',
                treeCount: 15,
                buildingCount: 40,
                obstacleCount: 15
            },
            5: { // State Capital
                name: 'State Capital',
                backgroundColor: '#E6E6FA',
                roadColor: '#1C1C1C',
                grassColor: '#ADFF2F',
                treeCount: 10,
                buildingCount: 60,
                obstacleCount: 20
            },
            6: { // International
                name: 'International',
                backgroundColor: '#F0F8FF',
                roadColor: '#000000',
                grassColor: '#7CFC00',
                treeCount: 5,
                buildingCount: 80,
                obstacleCount: 25
            }
        };

        this.currentConfig = this.levelConfigs[1];
    }

    setLevel(level) {
        this.currentLevel = level;
        this.currentConfig = this.levelConfigs[level];
        this.generateLevel();
    }

    generateLevel() {
        this.roads = [];
        this.buildings = [];
        this.trees = [];
        this.obstacles = [];
        this.decorations = [];

        const canvas = document.getElementById('gameCanvas');
        const width = canvas.width;
        const height = canvas.height;

        // Generate main road
        this.generateRoad(width, height);

        // Generate environment elements based on level
        this.generateTrees(width, height);
        this.generateBuildings(width, height);
        this.generateObstacles(width, height);
        this.generateDecorations(width, height);
    }

    generateRoad(width, height) {
        const roadWidth = 200;
        const roadX = width / 2 - roadWidth / 2;

        // Main road segments
        for (let y = -height; y < height * 3; y += 50) {
            this.roads.push({
                x: roadX,
                y: y,
                width: roadWidth,
                height: 50,
                type: 'main'
            });
        }

        // Road markings
        for (let y = -height; y < height * 3; y += 30) {
            this.roads.push({
                x: roadX + roadWidth / 2 - 2,
                y: y,
                width: 4,
                height: 15,
                type: 'marking'
            });
        }

        // Side roads (for higher levels)
        if (this.currentLevel >= 3) {
            for (let i = 0; i < 3; i++) {
                const sideY = height * i;
                this.roads.push({
                    x: roadX - 100,
                    y: sideY,
                    width: 100,
                    height: 30,
                    type: 'side'
                });
                this.roads.push({
                    x: roadX + roadWidth,
                    y: sideY,
                    width: 100,
                    height: 30,
                    type: 'side'
                });
            }
        }
    }

    generateTrees(width, height) {
        const treeCount = this.currentConfig.treeCount;

        for (let i = 0; i < treeCount; i++) {
            let x, y;
            let attempts = 0;

            do {
                x = Math.random() * width;
                y = Math.random() * height * 2 - height;
                attempts++;
            } while (this.isOnRoad(x, y) && attempts < 50);

            if (attempts < 50) {
                this.trees.push({
                    x: x,
                    y: y,
                    size: 15 + Math.random() * 25,
                    type: Math.random() > 0.5 ? 'oak' : 'pine',
                    swayOffset: Math.random() * Math.PI * 2
                });
            }
        }
    }

    generateBuildings(width, height) {
        const buildingCount = this.currentConfig.buildingCount;

        for (let i = 0; i < buildingCount; i++) {
            let x, y;
            let attempts = 0;

            do {
                x = Math.random() * width;
                y = Math.random() * height * 2 - height;
                attempts++;
            } while (this.isOnRoad(x, y, 60) && attempts < 50);

            if (attempts < 50) {
                const buildingWidth = 40 + Math.random() * 60;
                const buildingHeight = 30 + Math.random() * 80;

                this.buildings.push({
                    x: x - buildingWidth / 2,
                    y: y - buildingHeight,
                    width: buildingWidth,
                    height: buildingHeight,
                    type: this.getBuildingType(),
                    color: this.getBuildingColor(),
                    windows: this.generateWindows(buildingWidth, buildingHeight)
                });
            }
        }
    }

    generateObstacles(width, height) {
        const obstacleCount = this.currentConfig.obstacleCount;
        const roadWidth = 200;
        const roadX = width / 2 - roadWidth / 2;

        for (let i = 0; i < obstacleCount; i++) {
            const x = roadX + 20 + Math.random() * (roadWidth - 40);
            const y = Math.random() * height * 2 - height;

            this.obstacles.push({
                x: x,
                y: y,
                width: 20 + Math.random() * 20,
                height: 20 + Math.random() * 20,
                type: this.getObstacleType(),
                damage: 1 + Math.floor(Math.random() * 3)
            });
        }
    }

    generateDecorations(width, height) {
        // Street lights for higher levels
        if (this.currentLevel >= 3) {
            const roadWidth = 200;
            const roadX = width / 2 - roadWidth / 2;

            for (let y = -height; y < height * 3; y += 100) {
                this.decorations.push({
                    x: roadX - 10,
                    y: y,
                    type: 'streetlight',
                    height: 40
                });
                this.decorations.push({
                    x: roadX + roadWidth + 10,
                    y: y,
                    type: 'streetlight',
                    height: 40
                });
            }
        }

        // Traffic signs
        if (this.currentLevel >= 2) {
            for (let i = 0; i < 5; i++) {
                const roadWidth = 200;
                const roadX = width / 2 - roadWidth / 2;
                const x = roadX + (Math.random() > 0.5 ? -30 : roadWidth + 30);
                const y = Math.random() * height * 2 - height;

                this.decorations.push({
                    x: x,
                    y: y,
                    type: 'sign',
                    signType: Math.random() > 0.5 ? 'speed' : 'warning'
                });
            }
        }
    }

    getBuildingType() {
        const types = ['house', 'shop', 'office', 'apartment'];
        const weights = [0.4, 0.3, 0.2, 0.1];

        // Adjust weights based on level
        if (this.currentLevel >= 4) {
            weights[2] += 0.2; // More offices
            weights[3] += 0.2; // More apartments
        }

        const random = Math.random();
        let sum = 0;
        for (let i = 0; i < types.length; i++) {
            sum += weights[i];
            if (random < sum) return types[i];
        }
        return types[0];
    }

    getBuildingColor() {
        const colors = ['#8B4513', '#CD853F', '#D2691E', '#A0522D', '#DEB887'];

        // More modern colors for higher levels
        if (this.currentLevel >= 4) {
            colors.push('#C0C0C0', '#808080', '#696969', '#2F4F4F');
        }

        return colors[Math.floor(Math.random() * colors.length)];
    }

    getObstacleType() {
        const types = ['rock', 'pothole', 'debris', 'animal'];
        const levelTypes = {
            1: ['rock', 'animal'],
            2: ['rock', 'pothole', 'animal'],
            3: ['pothole', 'debris'],
            4: ['debris', 'pothole'],
            5: ['debris'],
            6: ['debris']
        };

        const availableTypes = levelTypes[this.currentLevel] || types;
        return availableTypes[Math.floor(Math.random() * availableTypes.length)];
    }

    generateWindows(buildingWidth, buildingHeight) {
        const windows = [];
        const windowSize = 8;
        const spacing = 15;

        for (let x = spacing; x < buildingWidth - windowSize; x += spacing) {
            for (let y = spacing; y < buildingHeight - windowSize; y += spacing) {
                if (Math.random() > 0.3) { // 70% chance for window
                    windows.push({ x, y, lit: Math.random() > 0.5 });
                }
            }
        }

        return windows;
    }

    isOnRoad(x, y, buffer = 30) {
        const canvas = document.getElementById('gameCanvas');
        const roadWidth = 200;
        const roadX = canvas.width / 2 - roadWidth / 2;

        return x > roadX - buffer && x < roadX + roadWidth + buffer;
    }

    update(deltaTime) {
        // Update scroll based on vehicle movement
        const vehicle = arguments[1]; // Vehicle passed from game
        if (vehicle) {
            this.scrollSpeed = vehicle.speed * 0.5;
            this.scrollY += this.scrollSpeed * deltaTime;
        }

        // Update animated elements
        this.updateAnimations(deltaTime);
    }

    updateAnimations(deltaTime) {
        // Sway trees
        this.trees.forEach(tree => {
            tree.swayOffset += deltaTime;
        });

        // Flicker street lights
        this.decorations.forEach(decoration => {
            if (decoration.type === 'streetlight') {
                decoration.flicker = Math.random() > 0.95;
            }
        });
    }

    checkCollisions(vehicle) {
        const collisions = [];
        const vehicleBounds = vehicle.getBounds();

        // Check obstacle collisions
        this.obstacles.forEach(obstacle => {
            if (this.isColliding(vehicleBounds, obstacle)) {
                collisions.push({
                    type: 'obstacle',
                    object: obstacle,
                    damage: obstacle.damage,
                    normal: this.getCollisionNormal(vehicle, obstacle)
                });
            }
        });

        // Check building collisions
        this.buildings.forEach(building => {
            if (this.isColliding(vehicleBounds, building)) {
                collisions.push({
                    type: 'obstacle',
                    object: building,
                    damage: 5,
                    normal: this.getCollisionNormal(vehicle, building)
                });
            }
        });

        // Check tree collisions
        this.trees.forEach(tree => {
            const treeBounds = {
                x: tree.x - tree.size/2,
                y: tree.y - tree.size/2,
                width: tree.size,
                height: tree.size
            };

            if (this.isColliding(vehicleBounds, treeBounds)) {
                collisions.push({
                    type: 'obstacle',
                    object: tree,
                    damage: 2,
                    normal: this.getCollisionNormal(vehicle, treeBounds)
                });
            }
        });

        return collisions;
    }

    isColliding(vehicleBounds, object) {
        // Simple AABB collision for now
        const vehicleRect = this.getBoundingRect(vehicleBounds);

        return vehicleRect.x < object.x + object.width &&
               vehicleRect.x + vehicleRect.width > object.x &&
               vehicleRect.y < object.y + object.height &&
               vehicleRect.y + vehicleRect.height > object.y;
    }

    getBoundingRect(bounds) {
        const xs = bounds.map(p => p.x);
        const ys = bounds.map(p => p.y);

        return {
            x: Math.min(...xs),
            y: Math.min(...ys),
            width: Math.max(...xs) - Math.min(...xs),
            height: Math.max(...ys) - Math.min(...ys)
        };
    }

    getCollisionNormal(vehicle, object) {
        const dx = vehicle.x - (object.x + object.width / 2);
        const dy = vehicle.y - (object.y + object.height / 2);
        const length = Math.sqrt(dx * dx + dy * dy);

        return {
            x: dx / length,
            y: dy / length
        };
    }

    render(ctx, vehicle) {
        const canvas = ctx.canvas;

        // Clear and set background
        ctx.fillStyle = this.currentConfig.backgroundColor;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Calculate camera offset
        const cameraY = vehicle ? vehicle.y - canvas.height * 0.7 : 0;

        ctx.save();
        ctx.translate(0, -cameraY);

        // Render grass/ground
        this.renderGround(ctx, canvas, cameraY);

        // Render roads
        this.renderRoads(ctx, cameraY);

        // Render environment elements (back to front)
        this.renderTrees(ctx, cameraY);
        this.renderBuildings(ctx, cameraY);
        this.renderDecorations(ctx, cameraY);
        this.renderObstacles(ctx, cameraY);

        ctx.restore();
    }

    renderGround(ctx, canvas, cameraY) {
        // Base grass color with gradient
        const grassGradient = ctx.createLinearGradient(0, cameraY - canvas.height, 0, cameraY + canvas.height * 2);
        grassGradient.addColorStop(0, this.currentConfig.grassColor);
        grassGradient.addColorStop(0.5, '#32CD32');
        grassGradient.addColorStop(1, '#228B22');

        ctx.fillStyle = grassGradient;
        ctx.fillRect(0, cameraY - canvas.height, canvas.width, canvas.height * 3);

        // Add realistic grass texture
        ctx.fillStyle = `rgba(0, 80, 0, 0.3)`;
        for (let i = 0; i < 200; i++) {
            const x = Math.random() * canvas.width;
            const y = cameraY - canvas.height + Math.random() * canvas.height * 3;
            const grassHeight = 3 + Math.random() * 4;
            ctx.fillRect(x, y, 1, grassHeight);
        }

        // Add darker grass patches for depth
        ctx.fillStyle = `rgba(0, 60, 0, 0.2)`;
        for (let i = 0; i < 50; i++) {
            const x = Math.random() * canvas.width;
            const y = cameraY - canvas.height + Math.random() * canvas.height * 3;
            const patchSize = 5 + Math.random() * 10;
            ctx.beginPath();
            ctx.arc(x, y, patchSize, 0, Math.PI * 2);
            ctx.fill();
        }

        // Add small flowers for village levels
        if (this.currentLevel <= 2) {
            ctx.fillStyle = '#FFB74D';
            for (let i = 0; i < 30; i++) {
                const x = Math.random() * canvas.width;
                const y = cameraY - canvas.height + Math.random() * canvas.height * 3;
                if (!this.isOnRoad(x, y, 50)) {
                    ctx.beginPath();
                    ctx.arc(x, y, 2, 0, Math.PI * 2);
                    ctx.fill();
                }
            }
        }

        // Add dirt patches for higher levels
        if (this.currentLevel >= 4) {
            ctx.fillStyle = '#8D6E63';
            for (let i = 0; i < 20; i++) {
                const x = Math.random() * canvas.width;
                const y = cameraY - canvas.height + Math.random() * canvas.height * 3;
                const patchSize = 8 + Math.random() * 15;
                ctx.beginPath();
                ctx.arc(x, y, patchSize, 0, Math.PI * 2);
                ctx.fill();
            }
        }
    }

    renderRoads(ctx, cameraY) {
        this.roads.forEach(road => {
            if (road.y > cameraY - 100 && road.y < cameraY + ctx.canvas.height + 100) {
                if (road.type === 'main' || road.type === 'side') {
                    // 3D road effect with gradient
                    const roadGradient = ctx.createLinearGradient(road.x, 0, road.x + road.width, 0);
                    roadGradient.addColorStop(0, '#555555');
                    roadGradient.addColorStop(0.1, this.currentConfig.roadColor);
                    roadGradient.addColorStop(0.9, this.currentConfig.roadColor);
                    roadGradient.addColorStop(1, '#333333');

                    ctx.fillStyle = roadGradient;
                    ctx.fillRect(road.x, road.y, road.width, road.height);

                    // Road edges with 3D effect
                    ctx.fillStyle = '#444444';
                    ctx.fillRect(road.x - 2, road.y, 4, road.height); // Left edge
                    ctx.fillRect(road.x + road.width - 2, road.y, 4, road.height); // Right edge

                    // Road border lines
                    ctx.strokeStyle = '#FFFF00';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(road.x, road.y);
                    ctx.lineTo(road.x, road.y + road.height);
                    ctx.moveTo(road.x + road.width, road.y);
                    ctx.lineTo(road.x + road.width, road.y + road.height);
                    ctx.stroke();

                    // Road texture (asphalt effect)
                    ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
                    for (let i = 0; i < 10; i++) {
                        const textureX = road.x + Math.random() * road.width;
                        const textureY = road.y + Math.random() * road.height;
                        ctx.fillRect(textureX, textureY, 1, 1);
                    }

                } else if (road.type === 'marking') {
                    // Enhanced road markings
                    ctx.fillStyle = '#FFFFFF';
                    ctx.fillRect(road.x, road.y, road.width, road.height);

                    // Add reflective effect to markings
                    ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
                    ctx.fillRect(road.x - 1, road.y, road.width + 2, road.height);
                }
            }
        });
    }

    renderTrees(ctx, cameraY) {
        this.trees.forEach(tree => {
            if (tree.y > cameraY - 200 && tree.y < cameraY + ctx.canvas.height + 200) {
                const sway = Math.sin(tree.swayOffset) * 3;

                ctx.save();
                ctx.translate(tree.x + sway, tree.y);

                // 3D-like tree shadow (larger and more realistic)
                ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
                ctx.beginPath();
                ctx.ellipse(5, 10, tree.size * 0.4, tree.size * 0.15, 0, 0, Math.PI * 2);
                ctx.fill();

                // Tree trunk with 3D effect
                const trunkGradient = ctx.createLinearGradient(-4, 0, 4, 0);
                trunkGradient.addColorStop(0, '#654321');
                trunkGradient.addColorStop(0.5, '#8B4513');
                trunkGradient.addColorStop(1, '#5D4037');
                ctx.fillStyle = trunkGradient;
                ctx.fillRect(-4, -tree.size * 0.3, 8, tree.size * 0.6);

                // Tree trunk highlight
                ctx.fillStyle = '#A0522D';
                ctx.fillRect(-4, -tree.size * 0.3, 2, tree.size * 0.6);

                // Multiple layers of foliage for 3D effect
                const layers = tree.type === 'oak' ? 3 : 2;
                for (let i = layers - 1; i >= 0; i--) {
                    const layerSize = tree.size * (0.3 + i * 0.1);
                    const layerY = -tree.size * (0.4 + i * 0.1);

                    // Foliage shadow
                    ctx.fillStyle = tree.type === 'oak' ? '#1B5E20' : '#2E7D32';
                    ctx.beginPath();
                    ctx.arc(2, layerY + 2, layerSize, 0, Math.PI * 2);
                    ctx.fill();

                    // Main foliage
                    const foliageGradient = ctx.createRadialGradient(
                        -layerSize * 0.3, layerY - layerSize * 0.3, 0,
                        0, layerY, layerSize
                    );
                    foliageGradient.addColorStop(0, tree.type === 'oak' ? '#4CAF50' : '#388E3C');
                    foliageGradient.addColorStop(0.7, tree.type === 'oak' ? '#2E7D32' : '#1B5E20');
                    foliageGradient.addColorStop(1, tree.type === 'oak' ? '#1B5E20' : '#0D4E12');

                    ctx.fillStyle = foliageGradient;
                    ctx.beginPath();
                    ctx.arc(0, layerY, layerSize, 0, Math.PI * 2);
                    ctx.fill();

                    // Foliage highlights
                    ctx.fillStyle = tree.type === 'oak' ? '#66BB6A' : '#4CAF50';
                    ctx.beginPath();
                    ctx.arc(-layerSize * 0.2, layerY - layerSize * 0.2, layerSize * 0.3, 0, Math.PI * 2);
                    ctx.fill();
                }

                // Add some leaves falling effect
                if (Math.random() > 0.98) {
                    ctx.fillStyle = tree.type === 'oak' ? '#8BC34A' : '#4CAF50';
                    for (let i = 0; i < 3; i++) {
                        const leafX = (Math.random() - 0.5) * tree.size;
                        const leafY = (Math.random() - 0.5) * tree.size;
                        ctx.fillRect(leafX, leafY, 2, 2);
                    }
                }

                ctx.restore();
            }
        });
    }

    renderBuildings(ctx, cameraY) {
        this.buildings.forEach(building => {
            if (building.y > cameraY - 200 && building.y < cameraY + ctx.canvas.height + 100) {
                // Building shadow
                ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
                ctx.fillRect(building.x + 5, building.y + 5, building.width, building.height);

                // Building body
                ctx.fillStyle = building.color;
                ctx.fillRect(building.x, building.y, building.width, building.height);

                // Building outline
                ctx.strokeStyle = '#000000';
                ctx.lineWidth = 1;
                ctx.strokeRect(building.x, building.y, building.width, building.height);

                // Windows
                building.windows.forEach(window => {
                    ctx.fillStyle = window.lit ? '#FFFF99' : '#87CEEB';
                    ctx.fillRect(building.x + window.x, building.y + window.y, 8, 8);

                    ctx.strokeStyle = '#000000';
                    ctx.lineWidth = 1;
                    ctx.strokeRect(building.x + window.x, building.y + window.y, 8, 8);
                });

                // Roof
                if (building.type === 'house') {
                    ctx.fillStyle = '#8B0000';
                    ctx.beginPath();
                    ctx.moveTo(building.x, building.y);
                    ctx.lineTo(building.x + building.width / 2, building.y - 20);
                    ctx.lineTo(building.x + building.width, building.y);
                    ctx.closePath();
                    ctx.fill();
                    ctx.stroke();
                }
            }
        });
    }

    renderDecorations(ctx, cameraY) {
        this.decorations.forEach(decoration => {
            if (decoration.y > cameraY - 100 && decoration.y < cameraY + ctx.canvas.height + 100) {
                if (decoration.type === 'streetlight') {
                    // Pole
                    ctx.fillStyle = '#696969';
                    ctx.fillRect(decoration.x - 2, decoration.y - decoration.height, 4, decoration.height);

                    // Light
                    ctx.fillStyle = decoration.flicker ? '#FFFF99' : '#FFFFCC';
                    ctx.beginPath();
                    ctx.arc(decoration.x, decoration.y - decoration.height, 8, 0, Math.PI * 2);
                    ctx.fill();

                    // Light glow
                    if (!decoration.flicker) {
                        const gradient = ctx.createRadialGradient(decoration.x, decoration.y - decoration.height, 0, decoration.x, decoration.y - decoration.height, 30);
                        gradient.addColorStop(0, 'rgba(255, 255, 153, 0.3)');
                        gradient.addColorStop(1, 'rgba(255, 255, 153, 0)');
                        ctx.fillStyle = gradient;
                        ctx.beginPath();
                        ctx.arc(decoration.x, decoration.y - decoration.height, 30, 0, Math.PI * 2);
                        ctx.fill();
                    }
                } else if (decoration.type === 'sign') {
                    // Sign post
                    ctx.fillStyle = '#696969';
                    ctx.fillRect(decoration.x - 1, decoration.y - 30, 2, 30);

                    // Sign board
                    ctx.fillStyle = decoration.signType === 'speed' ? '#FF0000' : '#FFFF00';
                    ctx.fillRect(decoration.x - 10, decoration.y - 40, 20, 15);

                    ctx.strokeStyle = '#000000';
                    ctx.lineWidth = 1;
                    ctx.strokeRect(decoration.x - 10, decoration.y - 40, 20, 15);

                    // Sign text
                    ctx.fillStyle = '#000000';
                    ctx.font = '8px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(decoration.signType === 'speed' ? '50' : '!', decoration.x, decoration.y - 30);
                }
            }
        });
    }

    renderObstacles(ctx, cameraY) {
        this.obstacles.forEach(obstacle => {
            if (obstacle.y > cameraY - 100 && obstacle.y < cameraY + ctx.canvas.height + 100) {
                ctx.save();

                switch(obstacle.type) {
                    case 'rock':
                        ctx.fillStyle = '#696969';
                        ctx.beginPath();
                        ctx.arc(obstacle.x + obstacle.width/2, obstacle.y + obstacle.height/2, obstacle.width/2, 0, Math.PI * 2);
                        ctx.fill();
                        ctx.strokeStyle = '#2F2F2F';
                        ctx.lineWidth = 2;
                        ctx.stroke();
                        break;

                    case 'pothole':
                        ctx.fillStyle = '#1C1C1C';
                        ctx.fillRect(obstacle.x, obstacle.y, obstacle.width, obstacle.height);
                        ctx.strokeStyle = '#8B4513';
                        ctx.lineWidth = 1;
                        ctx.strokeRect(obstacle.x, obstacle.y, obstacle.width, obstacle.height);
                        break;

                    case 'debris':
                        ctx.fillStyle = '#8B4513';
                        ctx.fillRect(obstacle.x, obstacle.y, obstacle.width, obstacle.height);
                        ctx.fillStyle = '#654321';
                        ctx.fillRect(obstacle.x + 2, obstacle.y + 2, obstacle.width - 4, obstacle.height - 4);
                        break;

                    case 'animal':
                        // Simple animal representation
                        ctx.fillStyle = '#8B4513';
                        ctx.beginPath();
                        ctx.arc(obstacle.x + obstacle.width/2, obstacle.y + obstacle.height/2, obstacle.width/2, 0, Math.PI * 2);
                        ctx.fill();

                        // Eyes
                        ctx.fillStyle = '#000000';
                        ctx.beginPath();
                        ctx.arc(obstacle.x + obstacle.width/2 - 3, obstacle.y + obstacle.height/2 - 2, 1, 0, Math.PI * 2);
                        ctx.arc(obstacle.x + obstacle.width/2 + 3, obstacle.y + obstacle.height/2 - 2, 1, 0, Math.PI * 2);
                        ctx.fill();
                        break;
                }

                ctx.restore();
            }
        });
    }
}
