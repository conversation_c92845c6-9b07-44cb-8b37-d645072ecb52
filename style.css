/* Village to World Car Game - Black, <PERSON>, Blue Theme */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #1a1a1a 0%, #2c2c2c 50%, #1a1a1a 100%);
    color: #ffd700;
    overflow: hidden;
}

#gameContainer {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* Header Styles */
#gameHeader {
    background: linear-gradient(90deg, #000000 0%, #1e3a8a 50%, #000000 100%);
    padding: 10px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2px solid #ffd700;
    box-shadow: 0 2px 10px rgba(255, 215, 0, 0.3);
}

#levelInfo, #gameStats {
    display: flex;
    gap: 20px;
}

#levelInfo span, #gameStats span {
    color: #ffd700;
    font-weight: bold;
    text-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
}

/* Game Canvas */
#gameCanvas {
    flex: 1;
    background: #87ceeb;
    border: 3px solid #ffd700;
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.3);
}

/* Game Controls */
#gameControls {
    position: absolute;
    bottom: 20px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    padding: 0 40px;
}

/* Modern Steering Wheel */
#steeringWheel {
    width: 140px;
    height: 140px;
    background: radial-gradient(circle, #2c2c2c 0%, #1a1a1a 40%, #000000 100%);
    border-radius: 50%;
    position: relative;
    border: 6px solid #ffd700;
    box-shadow:
        0 0 30px rgba(255, 215, 0, 0.6),
        inset 0 0 20px rgba(0, 0, 0, 0.5);
    cursor: pointer;
    transition: all 0.1s ease;
}

#steeringWheel:hover {
    box-shadow:
        0 0 40px rgba(255, 215, 0, 0.8),
        inset 0 0 20px rgba(0, 0, 0, 0.5);
}

#wheel {
    width: 100px;
    height: 100px;
    background: linear-gradient(45deg, #333333 0%, #666666 50%, #333333 100%);
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border: 3px solid #ffd700;
    box-shadow: inset 0 0 15px rgba(0, 0, 0, 0.7);
}

#wheelCenter {
    width: 30px;
    height: 30px;
    background: radial-gradient(circle, #ffd700 0%, #b8860b 100%);
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border: 2px solid #000000;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.8);
}

#wheel::before {
    content: '';
    position: absolute;
    width: 6px;
    height: 80px;
    background: linear-gradient(to bottom, #ffd700 0%, #b8860b 50%, #ffd700 100%);
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    border-radius: 3px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

#wheel::after {
    content: '';
    position: absolute;
    width: 80px;
    height: 6px;
    background: linear-gradient(to right, #ffd700 0%, #b8860b 50%, #ffd700 100%);
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    border-radius: 3px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

/* Modern Pedals Side by Side */
#pedals {
    display: flex;
    flex-direction: row;
    gap: 20px;
    align-items: center;
}

.pedal {
    width: 100px;
    height: 80px;
    border: none;
    border-radius: 15px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow:
        0 6px 12px rgba(0, 0, 0, 0.4),
        inset 0 2px 4px rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.pedal::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 30%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.3), transparent);
    border-radius: 15px 15px 0 0;
}

.accelerator {
    background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 50%, #1B5E20 100%);
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.accelerator:hover {
    background: linear-gradient(135deg, #66BB6A 0%, #4CAF50 50%, #2E7D32 100%);
    transform: translateY(-2px);
    box-shadow:
        0 8px 16px rgba(0, 0, 0, 0.5),
        inset 0 2px 4px rgba(255, 255, 255, 0.3);
}

.accelerator:active {
    background: linear-gradient(135deg, #2E7D32 0%, #1B5E20 50%, #0D4E12 100%);
    transform: translateY(2px);
    box-shadow:
        0 2px 6px rgba(0, 0, 0, 0.4),
        inset 0 4px 8px rgba(0, 0, 0, 0.3);
}

.brake {
    background: linear-gradient(135deg, #F44336 0%, #C62828 50%, #B71C1C 100%);
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.brake:hover {
    background: linear-gradient(135deg, #EF5350 0%, #F44336 50%, #C62828 100%);
    transform: translateY(-2px);
    box-shadow:
        0 8px 16px rgba(0, 0, 0, 0.5),
        inset 0 2px 4px rgba(255, 255, 255, 0.3);
}

.brake:active,
.brake.active {
    background: linear-gradient(135deg, #C62828 0%, #B71C1C 50%, #8E0000 100%);
    transform: translateY(2px);
    box-shadow:
        0 2px 6px rgba(0, 0, 0, 0.4),
        inset 0 4px 8px rgba(0, 0, 0, 0.3);
}

.accelerator:active,
.accelerator.active {
    background: linear-gradient(135deg, #2E7D32 0%, #1B5E20 50%, #0D4E12 100%);
    transform: translateY(2px);
    box-shadow:
        0 2px 6px rgba(0, 0, 0, 0.4),
        inset 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* Game UI */
#gameUI {
    position: absolute;
    top: 80px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

#miniMap {
    width: 200px;
    height: 150px;
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid #ffd700;
    border-radius: 10px;
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
}

#objective {
    width: 250px;
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid #1e3a8a;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 0 15px rgba(30, 58, 138, 0.3);
}

#objective h3 {
    color: #ffd700;
    margin-bottom: 10px;
    text-align: center;
}

#objective p {
    color: #ffffff;
    font-size: 14px;
    margin-bottom: 10px;
}

#progressBar {
    width: 100%;
    height: 20px;
    background: #333333;
    border-radius: 10px;
    overflow: hidden;
    border: 1px solid #ffd700;
}

#progress {
    height: 100%;
    background: linear-gradient(90deg, #ffd700 0%, #1e3a8a 100%);
    width: 0%;
    transition: width 0.3s ease;
}

/* Screen Overlays */
.screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.95);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    text-align: center;
}

.screen.hidden {
    display: none;
}

.screen h1 {
    font-size: 48px;
    color: #ffd700;
    margin-bottom: 20px;
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

.screen h2 {
    font-size: 36px;
    color: #ffd700;
    margin-bottom: 20px;
    text-shadow: 0 0 15px rgba(255, 215, 0, 0.5);
}

.screen p {
    font-size: 18px;
    color: #ffffff;
    margin-bottom: 30px;
    max-width: 600px;
    line-height: 1.6;
}

.screen button {
    padding: 15px 30px;
    font-size: 20px;
    font-weight: bold;
    background: linear-gradient(135deg, #ffd700 0%, #1e3a8a 100%);
    color: #000000;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
}

.screen button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
}

.screen button:active {
    transform: translateY(0);
}

/* Modern Control Enhancements */
.pedal:focus {
    outline: none;
    box-shadow:
        0 0 20px rgba(255, 215, 0, 0.8),
        0 6px 12px rgba(0, 0, 0, 0.4),
        inset 0 2px 4px rgba(255, 255, 255, 0.2);
}

#steeringWheel:active {
    transform: scale(0.98);
}

/* Add steering wheel grip texture */
#wheel::before {
    box-shadow:
        0 0 5px rgba(0, 0, 0, 0.5),
        inset 0 1px 2px rgba(255, 255, 255, 0.3);
}

#wheel::after {
    box-shadow:
        0 0 5px rgba(0, 0, 0, 0.5),
        inset 0 1px 2px rgba(255, 255, 255, 0.3);
}

/* Add pedal icons */
.accelerator::after {
    content: '⚡';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 24px;
    margin-top: -10px;
}

.brake::after {
    content: '🛑';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 20px;
    margin-top: -10px;
}

/* Responsive Design */
@media (max-width: 768px) {
    #gameHeader {
        padding: 8px 15px;
        font-size: 14px;
    }

    #gameControls {
        bottom: 10px;
        padding: 0 20px;
    }

    #steeringWheel {
        width: 100px;
        height: 100px;
    }

    #wheel {
        width: 70px;
        height: 70px;
    }

    #wheelCenter {
        width: 20px;
        height: 20px;
    }

    .pedal {
        width: 80px;
        height: 60px;
        font-size: 14px;
    }

    #pedals {
        gap: 15px;
    }

    #gameUI {
        top: 70px;
        right: 10px;
    }

    #miniMap {
        width: 150px;
        height: 100px;
    }

    #objective {
        width: 200px;
        padding: 10px;
    }
}

/* Animations */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes glow {
    0% { box-shadow: 0 0 5px rgba(255, 215, 0, 0.3); }
    50% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.8); }
    100% { box-shadow: 0 0 5px rgba(255, 215, 0, 0.3); }
}

.glow {
    animation: glow 2s infinite;
}
