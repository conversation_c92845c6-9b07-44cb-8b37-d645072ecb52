# Village to World - Car Game

A progressive car driving game that takes players on a journey from rural villages to international metropolitan cities, featuring realistic controls, safe driving mechanics, and beautiful environments.

## Game Features

### 🎮 Realistic Controls
- **On-screen steering wheel** - Touch/click and drag to steer
- **Accelerator pedal** - Press and hold to accelerate
- **<PERSON><PERSON><PERSON> pedal** - Press and hold to brake
- **Keyboard backup controls** - Arrow keys for alternative control

### 🌍 Progressive Level System
1. **Village** - Peaceful rural roads with greenery
2. **Mandal** - Small town with basic infrastructure
3. **District** - Medium development with more traffic
4. **Major City** - Urban environment with complex roads
5. **State Capital** - Metropolitan area with highways
6. **International** - Modern city with advanced infrastructure

### 🚗 Vehicle Physics
- Realistic acceleration and braking
- Steering mechanics with turning radius
- Speed-based handling
- Collision detection and damage system
- Vehicle trail effects

### 🎯 Safe Driving Mechanics
- **Safety Score** - Starts at 100%, decreases with collisions
- **Speed Limits** - Different for each level
- **Obstacle Avoidance** - Rocks, potholes, debris, animals
- **Target-based Objectives** - Reach destinations safely

### 🎨 Visual Design
- **Color Theme** - Black, Gold, and Blue
- **Dynamic Environments** - From rural green landscapes to urban cityscapes
- **Progressive Complexity** - Increasing detail and development
- **Day/Night Cycle** - Atmospheric lighting changes

### 🔊 Audio System
- **Procedural Sound Effects** - Engine, collision, brake sounds
- **Ambient Background Music** - Atmospheric soundscapes
- **Dynamic Engine Audio** - Speed-based pitch modulation
- **Level Completion Sounds** - Success and failure audio cues

## How to Play

1. **Start the Game** - Click "Start Journey" on the main screen
2. **Control Your Vehicle**:
   - Use the steering wheel to turn left/right
   - Press the accelerator (⬆) to speed up
   - Press the brake (⬇) to slow down
3. **Follow Objectives** - Drive safely to reach the target destination
4. **Avoid Obstacles** - Stay away from rocks, potholes, and other hazards
5. **Maintain Safety** - Keep your safety score above the required level
6. **Progress Through Levels** - Complete objectives to unlock new areas

## Technical Details

### Technologies Used
- **HTML5 Canvas** - For game rendering
- **JavaScript ES6** - Game logic and physics
- **Web Audio API** - Procedural sound generation
- **CSS3** - Styling and animations

### File Structure
```
├── index.html          # Main game page
├── style.css           # Game styling and theme
├── js/
│   ├── game.js         # Main game engine
│   ├── vehicle.js      # Vehicle physics and controls
│   ├── environment.js  # World rendering and collision
│   ├── levels.js       # Level progression system
│   ├── ui.js          # User interface management
│   └── audio.js       # Audio system
└── README.md          # This file
```

### Game Architecture
- **Modular Design** - Separate classes for different game systems
- **Component-based** - Vehicle, Environment, Levels, UI, Audio
- **Event-driven** - User input handling and game state management
- **Performance Optimized** - Efficient rendering and collision detection

## Controls

### Touch/Mouse Controls
- **Steering Wheel** - Click/touch and drag to steer
- **Accelerator Button** - Press and hold to accelerate
- **Brake Button** - Press and hold to brake

### Keyboard Controls (Backup)
- **Arrow Left/Right** - Steer left/right
- **Arrow Up** - Accelerate
- **Arrow Down** - Brake

## Game Mechanics

### Safety System
- Start with 100% safety score
- Lose points for collisions and reckless driving
- Game over if safety drops to 0%
- Different safety requirements for each level

### Speed Limits
- Village: 60 km/h
- Mandal: 70 km/h
- District: 80 km/h
- Major City: 90 km/h
- State Capital: 100 km/h
- International: 120 km/h

### Scoring System
- Base points for completing objectives
- Time bonus for faster completion
- Safety bonus for maintaining high safety score
- Speed violation penalties

## Installation

1. Clone or download the project files
2. Open `index.html` in a modern web browser
3. Start playing!

No additional installation or setup required.

## Browser Compatibility

- **Chrome** - Full support
- **Firefox** - Full support
- **Safari** - Full support
- **Edge** - Full support

Requires a modern browser with HTML5 Canvas and Web Audio API support.

## Development

### Adding New Levels
1. Add level configuration in `js/levels.js`
2. Update environment generation in `js/environment.js`
3. Add level-specific assets if needed

### Customizing Vehicle Physics
- Modify parameters in `js/vehicle.js`
- Adjust acceleration, braking, and steering values
- Fine-tune collision response

### Adding Sound Effects
- Use `js/audio.js` to generate new procedural sounds
- Add sound triggers in appropriate game events
- Customize volume and audio parameters

## Future Enhancements

- **Multiplayer Mode** - Race with friends
- **Vehicle Customization** - Different car models and colors
- **Weather Effects** - Rain, snow, fog
- **Traffic System** - AI-controlled vehicles
- **Achievement System** - Unlock rewards and badges
- **Mobile App** - Native mobile version

## Credits

Developed as a progressive car driving game featuring realistic controls and safe driving mechanics.

## License

This project is open source and available under the MIT License.
